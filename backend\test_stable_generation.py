#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试稳定的4张图片生成功能
"""

import asyncio
import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.services.image_generation_service import ImageGenerationService


async def test_stable_generation():
    """测试稳定的4张图片生成"""
    
    print("🧪 测试稳定的4张图片生成功能...")
    print("=" * 60)
    
    try:
        # 创建图像生成服务
        image_service = ImageGenerationService()
        print("✅ 图像生成服务实例化成功")
        
        # 测试参数
        prompt = "请为我生成文字'稳定'的前臂纹身图，艺术字体，适合纹身，文化传统，精美作品，最佳质量，超高细节，清晰锐利"
        num_images = 4
        
        print(f"📝 测试参数:")
        print(f"   提示词: {prompt}")
        print(f"   图片数量: {num_images}")
        print(f"   高分辨率: False (会生成水印)")
        
        # 调用图像生成
        print(f"\n🎨 开始生成图片...")
        result = await image_service.generate_tattoo_image(
            prompt=prompt,
            high_resolution=False,
            num_images=num_images
        )
        
        print("✅ 图像生成完成！")
        
        # 验证结果
        print(f"\n📊 生成结果验证:")
        
        if "images" in result:
            # 多张图片模式
            print(f"   模式: 多张图片")
            print(f"   实际生成数量: {len(result['images'])}")
            print(f"   请求数量: {result.get('requested_count', 'N/A')}")
            print(f"   总数: {result.get('total_count', 'N/A')}")
            
            # 验证每张图片的完整性
            success_count = 0
            for i, img in enumerate(result['images'], 1):
                print(f"\n   图片{i}验证:")
                print(f"     原图URL: {img.get('image_url', 'N/A')}")
                print(f"     缩略图URL: {img.get('thumbnail_url', 'N/A')}")
                print(f"     显示版URL: {img.get('watermarked_url', 'N/A')}")
                print(f"     预览版URL: {img.get('preview_url', 'N/A')}")
                print(f"     尺寸: {img.get('width', 'N/A')}x{img.get('height', 'N/A')}")
                print(f"     序号: {img.get('index', 'N/A')}")
                print(f"     水印状态: {img.get('is_watermarked', 'N/A')}")
                
                # 检查必要字段
                required_fields = ['image_url', 'thumbnail_url', 'watermarked_url', 'preview_url']
                missing_fields = [field for field in required_fields if not img.get(field)]
                
                if not missing_fields:
                    print(f"     ✅ 图片{i}完整")
                    success_count += 1
                else:
                    print(f"     ❌ 图片{i}缺少字段: {missing_fields}")
            
            print(f"\n📈 生成统计:")
            print(f"   成功图片: {success_count}/{num_images}")
            print(f"   成功率: {success_count/num_images*100:.1f}%")
            
            if success_count == num_images:
                print(f"🎉 完美！生成了完整的{num_images}张图片")
                return True
            elif success_count >= num_images * 0.75:  # 75%成功率也算可接受
                print(f"✅ 良好！生成了{success_count}张图片（75%以上成功率）")
                return True
            else:
                print(f"⚠️ 生成数量不足，成功率过低")
                return False
        else:
            # 单张图片模式
            print(f"   模式: 单张图片")
            print(f"   图片URL: {result.get('image_url', 'N/A')}")
            
            if num_images == 1:
                print(f"✅ 单张图片模式正确")
                return True
            else:
                print(f"❌ 期望{num_images}张图片，但返回单张图片模式")
                return False
                
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        print("详细错误信息:")
        traceback.print_exc()
        return False


async def test_multiple_rounds():
    """测试多轮生成的稳定性"""
    
    print("\n🔄 测试多轮生成稳定性...")
    print("=" * 60)
    
    success_rounds = 0
    total_rounds = 3
    
    for round_num in range(1, total_rounds + 1):
        print(f"\n🎯 第{round_num}轮测试:")
        
        try:
            image_service = ImageGenerationService()
            prompt = f"请为我生成文字'测试{round_num}'的前臂纹身图，艺术字体，适合纹身，文化传统，精美作品，最佳质量，超高细节，清晰锐利"
            
            result = await image_service.generate_tattoo_image(
                prompt=prompt,
                high_resolution=False,
                num_images=4
            )
            
            if "images" in result and len(result['images']) >= 3:  # 至少3张算成功
                print(f"   ✅ 第{round_num}轮成功: {len(result['images'])}/4张")
                success_rounds += 1
            else:
                print(f"   ❌ 第{round_num}轮失败: {len(result.get('images', []))}/4张")
                
        except Exception as e:
            print(f"   ❌ 第{round_num}轮异常: {str(e)}")
    
    print(f"\n📊 多轮测试结果:")
    print(f"   成功轮数: {success_rounds}/{total_rounds}")
    print(f"   稳定性: {success_rounds/total_rounds*100:.1f}%")
    
    return success_rounds >= total_rounds * 0.67  # 67%稳定性算合格


async def main():
    """主函数"""
    
    print("🧪 稳定图片生成测试")
    print("=" * 60)
    
    # 测试1: 单次4张图片生成
    test1_success = await test_stable_generation()
    
    # 测试2: 多轮生成稳定性
    test2_success = await test_multiple_rounds()
    
    print("\n" + "=" * 60)
    print("📋 测试总结:")
    print(f"   单次生成测试: {'✅ 通过' if test1_success else '❌ 失败'}")
    print(f"   多轮稳定性测试: {'✅ 通过' if test2_success else '❌ 失败'}")
    
    if test1_success and test2_success:
        print("🎉 所有测试通过！图片生成功能稳定")
        print("💡 现在应该能稳定生成4张图片")
    else:
        print("⚠️ 部分测试失败，需要进一步优化")
    print("=" * 60)


if __name__ == "__main__":
    asyncio.run(main())
