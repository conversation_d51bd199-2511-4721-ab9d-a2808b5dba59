<template>
  <div class="dashboard-page">
    <!-- 页面头部 -->
    <section class="dashboard-header">
      <div class="container">
        <div class="header-content">
          <div class="welcome-section">
            <h1>欢迎回来，{{ userStore.user?.full_name || userStore.user?.username }}！</h1>
            <p>管理您的纹身设计和账户信息</p>
          </div>
          <div class="user-stats">
            <div class="stat-card">
              <div class="stat-icon">
                <el-icon><User /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-value">{{ userStore.user?.username || '用户' }}</div>
                <div class="stat-label">当前用户</div>
              </div>
            </div>
            <div class="stat-card">
              <div class="stat-icon">
                <el-icon><Picture /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-value">{{ totalImagesCount }}</div>
                <div class="stat-label">生成图片总数</div>
              </div>
            </div>
            <div class="stat-card premium" v-if="userStore.user?.is_premium">
              <div class="stat-icon">
                <el-icon><Star /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-value">高级</div>
                <div class="stat-label">会员状态</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>



    <!-- 纹身图生成历史 -->
    <section class="recent-designs">
      <div class="container">
        <div class="section-header">
          <h2>纹身图生成历史</h2>
          <el-button type="text" @click="$router.push('/profile?tab=designs')">
            查看全部
            <el-icon><ArrowRight /></el-icon>
          </el-button>
        </div>
        
        <div v-if="recentDesigns.length > 0" class="designs-grid">
          <div 
            v-for="design in recentDesigns" 
            :key="design.id"
            class="design-card"
            @click="viewDesignDetail(design)"
          >
            <div class="design-image">
              <!-- 显示带水印的缩略图 -->
              <img :src="`http://localhost:8000${design.watermarked_url || design.thumbnail_url}`" :alt="design.content" />
              <div class="design-overlay">
                <div class="overlay-actions">
                  <el-button type="primary" size="small" @click.stop="previewImage(design)">
                    <el-icon><View /></el-icon>
                    预览
                  </el-button>
                  <el-button
                    type="primary"
                    size="small"
                    @click.stop="purchaseHighRes(design)"
                    v-if="design.is_watermarked"
                  >
                    <el-icon><Download /></el-icon>
                    购买高清
                  </el-button>
                  <el-button
                    type="success"
                    size="small"
                    @click.stop="downloadImage(design)"
                    v-else
                  >
                    <el-icon><Download /></el-icon>
                    下载
                  </el-button>
                </div>
              </div>
            </div>
            <div class="design-info">
              <div class="design-content">{{ design.content }}</div>
              <div class="design-meta">
                <span class="design-style">{{ design.style }}</span>
                <span class="design-date">{{ formatDate(design.created_at) }}</span>
              </div>
            </div>
          </div>
        </div>
        
        <div v-else class="empty-designs">
          <el-icon size="64"><Picture /></el-icon>
          <h3>还没有生成纹身图</h3>
          <p>开始生成您的第一个纹身设计吧！</p>
          <el-button type="primary" @click="$router.push('/recommend')">
            开始生成
          </el-button>
        </div>
      </div>
    </section>


  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import {
  Picture, Star, User,
  ArrowRight, View, Download
} from '@element-plus/icons-vue'
import { useUserStore } from '@/stores/counter'
import { TattooService } from '@/services/tattoo'
import type { TattooImage } from '@/types'

const router = useRouter()
const userStore = useUserStore()

const recentDesigns = ref<TattooImage[]>([])
const totalImagesCount = ref(0)

onMounted(async () => {
  // 检查用户登录状态
  if (!userStore.isAuthenticated) {
    router.push('/login')
    return
  }

  // 加载用户数据
  await loadUserData()
})

// 加载用户数据
const loadUserData = async () => {
  try {
    // 加载最近的设计（显示用）
    const designs = await TattooService.getUserTattooHistory({ limit: 6 })
    recentDesigns.value = designs

    // 加载统计数据
    const stats = await TattooService.getUserTattooStats()
    totalImagesCount.value = stats.total_images

  } catch (error) {
    console.error('加载用户数据失败:', error)
  }
}

// 查看设计详情
const viewDesignDetail = (design: TattooImage) => {
  router.push({
    path: '/tattoo-design',
    query: {
      content: design.content,
      style: design.style,
      position: design.position
    }
  })
}



// 格式化日期
const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleDateString('zh-CN')
}

// 预览图片（预览时也显示带水印版本，保护版权）
const previewImage = (image: TattooImage) => {
  // 如果已购买（无水印），显示原图
  if (!image.is_watermarked) {
    window.open(`http://localhost:8000${image.image_url}`, '_blank')
  } else {
    // 未购买时，预览显示768x768带水印版本（比页面显示的512x512大一些）
    window.open(`http://localhost:8000${image.preview_url || image.watermarked_url || image.thumbnail_url}`, '_blank')
  }
}

// 购买高清版
const purchaseHighRes = async (image: TattooImage) => {
  try {
    const result = await TattooService.unlockHighResolution(image.id)

    // 更新图片信息
    const index = recentDesigns.value.findIndex(img => img.id === image.id)
    if (index !== -1) {
      recentDesigns.value[index] = { ...result, is_watermarked: false }
    }

    ElMessage.success('高清版本解锁成功！')

  } catch (error) {
    console.error('购买高清版失败:', error)
    ElMessage.error('购买失败，请稍后重试')
  }
}

// 下载图片
const downloadImage = (image: TattooImage) => {
  const link = document.createElement('a')
  link.href = `http://localhost:8000${image.image_url}`
  link.download = `tattoo-${image.content}-${Date.now()}.png`
  link.click()
}
</script>

<style scoped>
.dashboard-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
}

/* 页面头部 */
.dashboard-header {
  padding: 60px 0 40px;
  background: linear-gradient(135deg, rgba(212, 175, 55, 0.1) 0%, rgba(255, 215, 0, 0.05) 100%);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 24px;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 30px;
}

.welcome-section h1 {
  font-size: 2.5rem;
  font-weight: 600;
  color: #ffffff;
  margin: 0 0 8px 0;
  font-family: 'SimSun', serif;
}

.welcome-section p {
  font-size: 1.1rem;
  color: rgba(255, 255, 255, 0.7);
  margin: 0;
}

.user-stats {
  display: flex;
  gap: 20px;
}

.stat-card {
  display: flex;
  align-items: center;
  gap: 12px;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  padding: 16px 20px;
  backdrop-filter: blur(10px);
  min-width: 120px;
}

.stat-card.premium {
  background: linear-gradient(135deg, rgba(212, 175, 55, 0.2) 0%, rgba(255, 215, 0, 0.1) 100%);
  border-color: rgba(255, 215, 0, 0.3);
}

.stat-icon {
  width: 40px;
  height: 40px;
  background: linear-gradient(135deg, #d4af37 0%, #ffd700 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #1a1a1a;
  font-size: 18px;
}

.stat-value {
  font-size: 1.5rem;
  font-weight: 600;
  color: #ffffff;
  line-height: 1;
}

.stat-label {
  font-size: 0.85rem;
  color: rgba(255, 255, 255, 0.7);
}



/* 最近的设计 */
.recent-designs {
  padding: 60px 0;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
}

.section-header h2 {
  font-size: 2rem;
  font-weight: 600;
  color: #ffffff;
  margin: 0;
}

.designs-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 20px;
}

.design-card {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  overflow: hidden;
  cursor: pointer;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
}

.design-card:hover {
  transform: translateY(-5px);
  background: rgba(255, 255, 255, 0.08);
  border-color: rgba(255, 215, 0, 0.3);
}

.design-image {
  position: relative;
  width: 100%;
  height: 150px;
  overflow: hidden;
}

.design-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.design-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(to bottom, transparent 0%, rgba(0, 0, 0, 0.8) 100%);
  display: flex;
  align-items: flex-end;
  justify-content: center;
  padding: 12px;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.overlay-actions {
  display: flex;
  gap: 8px;
  justify-content: center;
}

.design-card:hover .design-overlay {
  opacity: 1;
}

.design-info {
  padding: 12px;
}

.design-content {
  font-size: 1.1rem;
  font-weight: 600;
  color: #ffd700;
  font-family: 'SimSun', serif;
  margin-bottom: 4px;
}

.design-meta {
  display: flex;
  justify-content: space-between;
  font-size: 0.85rem;
  color: rgba(255, 255, 255, 0.7);
}

.empty-designs {
  text-align: center;
  padding: 60px 20px;
  color: rgba(255, 255, 255, 0.6);
}

.empty-designs h3 {
  color: #ffffff;
  margin: 16px 0 8px 0;
  font-size: 1.5rem;
}

.empty-designs p {
  margin: 0 0 24px 0;
}







/* 响应式设计 */
@media (max-width: 768px) {
  .container {
    padding: 0 16px;
  }

  .header-content {
    flex-direction: column;
    text-align: center;
    gap: 16px;
  }

  .user-stats {
    flex-wrap: wrap;
    justify-content: center;
    gap: 12px;
  }

  .stat-card {
    min-width: 120px;
    padding: 16px 12px;
  }

  .stat-value {
    font-size: 1.5rem;
  }

  .stat-label {
    font-size: 0.8rem;
  }

  .welcome-section h1 {
    font-size: 1.8rem;
    margin-bottom: 8px;
  }

  .welcome-section p {
    font-size: 1rem;
  }

  .designs-grid {
    grid-template-columns: repeat(auto-fill, minmax(140px, 1fr));
    gap: 12px;
  }

  .design-card {
    padding: 12px;
  }

  .design-image {
    height: 120px;
  }

  .design-info h4 {
    font-size: 1rem;
  }

  .design-meta {
    font-size: 0.75rem;
  }

  .design-actions {
    gap: 4px;
  }

  .design-actions .el-button {
    padding: 4px 8px;
    font-size: 0.75rem;
  }

  .section-title {
    font-size: 1.5rem;
  }

  .empty-designs {
    padding: 40px 16px;
  }

  .empty-designs h3 {
    font-size: 1.2rem;
  }

  .empty-designs p {
    font-size: 0.9rem;
  }
}
</style>
