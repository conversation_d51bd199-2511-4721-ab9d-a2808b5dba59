#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试数据库迁移后的API功能
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from sqlalchemy.orm import Session
from app.db.database import get_db
from app.models.tattoo import Tattoo<PERSON>mage, TattooRequest
from app.models.user import User


def test_api_after_migration():
    """测试迁移后的API功能"""
    
    print("🧪 测试数据库迁移后的API功能...")
    print("=" * 60)
    
    try:
        # 获取数据库会话
        db = next(get_db())
        
        # 查找一个用户
        user = db.query(User).first()
        if not user:
            print("❌ 没有找到用户")
            return False
            
        print(f"✅ 找到用户: {user.username}")
        
        # 测试查询用户的纹身图片历史（这是之前出错的查询）
        tattoo_images = (
            db.query(TattooImage)
            .join(TattooRequest)
            .filter(TattooRequest.user_id == user.id)
            .order_by(TattooImage.created_at.desc())
            .limit(10)
            .all()
        )
        
        print(f"📊 查询结果:")
        print(f"   用户ID: {user.id}")
        print(f"   纹身图片数量: {len(tattoo_images)}")
        
        if tattoo_images:
            print(f"   最新图片信息:")
            for i, img in enumerate(tattoo_images[:3], 1):
                print(f"     {i}. ID: {img.id}")
                print(f"        内容: {img.content}")
                print(f"        原图URL: {img.image_url}")
                print(f"        缩略图URL: {img.thumbnail_url}")
                print(f"        显示版URL: {img.watermarked_url}")
                print(f"        预览版URL: {img.preview_url}")  # 新字段
                print(f"        创建时间: {img.created_at}")
                print()
        else:
            print("   没有找到纹身图片")
            
            # 检查是否有纹身请求
            requests = db.query(TattooRequest).filter(TattooRequest.user_id == user.id).all()
            print(f"   纹身请求数量: {len(requests)}")
            
            # 检查是否有任何纹身图片
            all_images = db.query(TattooImage).all()
            print(f"   系统中总图片数量: {len(all_images)}")
            
            if all_images:
                print("   最新图片的字段信息:")
                latest_img = all_images[0]
                print(f"     ID: {latest_img.id}")
                print(f"     原图URL: {latest_img.image_url}")
                print(f"     缩略图URL: {latest_img.thumbnail_url}")
                print(f"     显示版URL: {latest_img.watermarked_url}")
                print(f"     预览版URL: {latest_img.preview_url}")  # 新字段
        
        print("✅ API查询测试成功！")
        return True
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        print("详细错误信息:")
        traceback.print_exc()
        return False
    finally:
        db.close()


def main():
    """主函数"""
    
    print("🧪 数据库迁移后API测试")
    print("=" * 60)
    
    success = test_api_after_migration()
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 API测试通过！")
        print("💡 数据库迁移成功，API可以正常工作")
        print("💡 现在可以重启后端服务并测试前端")
    else:
        print("❌ API测试失败")
        print("💡 请检查数据库连接和表结构")
    print("=" * 60)


if __name__ == "__main__":
    main()
