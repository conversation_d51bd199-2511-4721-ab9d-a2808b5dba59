#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
添加 preview_url 字段到 tattoo_images 表
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from sqlalchemy import text
from app.db.database import get_db


def add_preview_url_column():
    """添加 preview_url 字段到 tattoo_images 表"""
    
    print("🔧 开始添加 preview_url 字段...")
    
    try:
        # 获取数据库连接
        db = next(get_db())
        
        # 检查字段是否已存在
        check_sql = """
        SELECT COLUMN_NAME 
        FROM INFORMATION_SCHEMA.COLUMNS 
        WHERE TABLE_NAME = 'tattoo_images' 
        AND COLUMN_NAME = 'preview_url'
        AND TABLE_SCHEMA = DATABASE()
        """
        
        result = db.execute(text(check_sql)).fetchone()
        
        if result:
            print("✅ preview_url 字段已存在，无需添加")
            return True
        
        # 添加字段
        alter_sql = """
        ALTER TABLE tattoo_images 
        ADD COLUMN preview_url VARCHAR(500) NULL 
        COMMENT '带水印预览版本(768x768)'
        """
        
        print("📝 执行SQL: ALTER TABLE tattoo_images ADD COLUMN preview_url...")
        db.execute(text(alter_sql))
        db.commit()
        
        print("✅ preview_url 字段添加成功！")
        
        # 验证字段是否添加成功
        verify_result = db.execute(text(check_sql)).fetchone()
        if verify_result:
            print("✅ 字段验证成功")
            return True
        else:
            print("❌ 字段验证失败")
            return False
            
    except Exception as e:
        print(f"❌ 添加字段失败: {e}")
        db.rollback()
        return False
    finally:
        db.close()


def main():
    """主函数"""
    
    print("🗄️ 数据库迁移：添加 preview_url 字段")
    print("=" * 50)
    
    success = add_preview_url_column()
    
    print("\n" + "=" * 50)
    if success:
        print("🎉 数据库迁移完成！")
        print("💡 现在可以重启后端服务")
    else:
        print("❌ 数据库迁移失败")
        print("💡 请检查数据库连接和权限")
    print("=" * 50)


if __name__ == "__main__":
    main()
