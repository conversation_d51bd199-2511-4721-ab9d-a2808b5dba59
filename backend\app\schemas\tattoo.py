"""
纹身相关数据模式
Tattoo Related Data Schemas
"""

from pydantic import BaseModel
from typing import List, Optional, Dict, Any
from datetime import datetime


class TattooRequestBase(BaseModel):
    """纹身请求基础模式"""

    keywords: List[str]
    description: Optional[str] = None
    personal_meaning: Optional[str] = None
    selected_type: Optional[str] = None  # 'character' 或 'idiom'
    selected_content: Optional[str] = None
    preferred_style: Optional[str] = None
    preferred_position: Optional[str] = None
    size_preference: Optional[str] = None


class TattooRequestCreate(TattooRequestBase):
    """纹身请求创建模式"""

    pass


class TattooRequestUpdate(BaseModel):
    """纹身请求更新模式"""

    selected_type: Optional[str] = None
    selected_content: Optional[str] = None
    preferred_style: Optional[str] = None
    preferred_position: Optional[str] = None
    size_preference: Optional[str] = None
    ai_explanation: Optional[str] = None


class TattooRequestResponse(TattooRequestBase):
    """纹身请求响应模式"""

    id: int
    user_id: int
    recommended_characters: Optional[Dict[str, Any]] = None
    recommended_idioms: Optional[Dict[str, Any]] = None
    ai_explanation: Optional[str] = None
    status: str
    is_paid: bool
    created_at: datetime
    updated_at: Optional[datetime] = None

    class Config:
        from_attributes = True


class TattooImageBase(BaseModel):
    """纹身图片基础模式"""

    style: str
    position: str
    content: str


class TattooImageCreate(TattooImageBase):
    """纹身图片创建模式"""

    request_id: int
    image_url: str
    thumbnail_url: Optional[str] = None
    watermarked_url: Optional[str] = None
    width: Optional[int] = None
    height: Optional[int] = None
    file_size: Optional[int] = None
    generation_prompt: Optional[str] = None
    generation_model: Optional[str] = None
    generation_time: Optional[float] = None
    is_high_resolution: bool = False
    is_watermarked: bool = True
    quality_score: Optional[float] = None


class TattooImageResponse(TattooImageBase):
    """纹身图片响应模式"""

    id: int
    request_id: int
    image_url: str
    thumbnail_url: Optional[str] = None
    watermarked_url: Optional[str] = None
    preview_url: Optional[str] = None
    width: Optional[int] = None
    height: Optional[int] = None
    file_size: Optional[int] = None
    generation_prompt: Optional[str] = None
    generation_model: Optional[str] = None
    generation_time: Optional[float] = None
    is_high_resolution: bool
    is_watermarked: bool
    quality_score: Optional[float] = None
    created_at: datetime

    class Config:
        from_attributes = True


class TattooGenerationRequest(BaseModel):
    """纹身生成请求模式"""

    request_id: int
    style: str
    position: str
    content: str
    high_resolution: bool = False


class TattooStyleBase(BaseModel):
    """纹身风格基础模式"""

    name: str
    name_zh: str
    description: Optional[str] = None
    difficulty_level: int = 1
    is_premium: bool = False


class TattooStyleCreate(TattooStyleBase):
    """纹身风格创建模式"""

    characteristics: Optional[Dict[str, Any]] = None
    suitable_positions: Optional[List[str]] = None
    example_images: Optional[List[str]] = None
    prompt_template: Optional[str] = None


class TattooStyleResponse(TattooStyleBase):
    """纹身风格响应模式"""

    id: int
    characteristics: Optional[Dict[str, Any]] = None
    suitable_positions: Optional[List[str]] = None
    example_images: Optional[List[str]] = None
    prompt_template: Optional[str] = None
    is_active: bool
    created_at: datetime
    updated_at: Optional[datetime] = None

    class Config:
        from_attributes = True


class TattooGenerationResponse(BaseModel):
    """纹身生成响应模式"""

    image_id: int
    image_url: str
    watermarked_url: Optional[str] = None
    thumbnail_url: Optional[str] = None
    generation_time: float
    is_high_resolution: bool
    credits_used: int
    remaining_credits: int


class TattooMultiImageResponse(BaseModel):
    """多张纹身图片生成响应模式"""

    images: List[TattooImageResponse]
    total_count: int
    generation_time: float
    credits_used: int
    remaining_credits: int
