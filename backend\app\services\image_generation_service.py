"""
图像生成服务
Image Generation Service using AI APIs
"""

import os
import uuid
import requests
from typing import Dict, Any, Optional
from PIL import Image, ImageDraw, ImageFont
from app.core.config import settings


class ImageGenerationService:
    """图像生成服务类"""

    def __init__(self):
        self.image_api_key = settings.IMAGE_AI_API_KEY
        self.image_api_url = settings.IMAGE_AI_API_URL
        self.image_model = settings.IMAGE_AI_MODEL
        self.upload_dir = settings.UPLOAD_DIR

        # 确保上传目录存在
        os.makedirs(self.upload_dir, exist_ok=True)

    async def generate_tattoo_image(
        self, prompt: str, high_resolution: bool = False, num_images: int = 1
    ) -> Dict[str, Any]:
        """
        生成纹身图片

        Args:
            prompt: 生成提示词
            high_resolution: 是否生成高分辨率图片
            num_images: 生成图片数量 (1-4)

        Returns:
            包含图片信息的字典
        """

        try:
            if not self.image_api_key:
                raise Exception("未配置图像生成API密钥")

            # 验证图片数量参数
            if num_images < 1 or num_images > 4:
                raise Exception("图片数量必须在1-4之间")

            # 只使用火山引擎 Doubao AI，不使用模拟器
            return await self._generate_with_doubao_ai(prompt, high_resolution, num_images)

        except Exception as e:
            print(f"图像生成失败: {str(e)}")
            raise e

    async def _generate_with_doubao_ai(self, prompt: str, high_resolution: bool, num_images: int = 1) -> Dict[str, Any]:
        """使用火山引擎Doubao AI生成图片"""

        # 直接使用传入的提示词（已在tattoo_service中构建完成）
        tattoo_prompt = prompt

        # 打印实际发送的提示词，便于调试
        print(f"🎨 发送给火山引擎的提示词: {tattoo_prompt}")
        print(f"📊 请求参数 - 高分辨率: {high_resolution}, 图片数量: {num_images}")

        # 火山引擎API配置
        api_url = f"{self.image_api_url}/images/generations"

        # 根据分辨率设置参数 - 提供真正的高清尺寸
        size = "832x1248" if high_resolution else "1024x1024"

        headers = {"Authorization": f"Bearer {self.image_api_key}", "Content-Type": "application/json"}

        # 火山引擎单次只能生成1张图片，所以我们需要连续调用多次
        all_images_data = []

        for i in range(num_images):
            print(f"🔄 正在生成第 {i+1}/{num_images} 张图片...")

            # 添加重试机制
            max_retries = 2
            success = False

            for retry in range(max_retries + 1):
                try:
                    if retry > 0:
                        print(f"🔄 第 {i+1} 张图片重试第 {retry} 次...")

                    data = {
                        "model": self.image_model,
                        "prompt": tattoo_prompt,
                        "response_format": "url",
                        "size": size,
                        "guidance_scale": 7.5,
                        "watermark": False,  # 统一不使用火山引擎水印，使用我们自己的水印逻辑
                        # 移除 "n" 参数，因为火山引擎不支持多张图片
                    }

                    # 打印API请求参数（仅第一次）
                    if i == 0 and retry == 0:
                        print(f"🔧 火山引擎API请求参数: {data}")

                    response = requests.post(api_url, headers=headers, json=data, timeout=300)  # 5分钟超时

                    if response.status_code == 200:
                        result = response.json()
                        print(f"✅ 第 {i+1} 张图片生成成功")

                        # 处理单张图片（火山引擎每次返回一张）
                        if result["data"] and len(result["data"]) > 0:
                            image_data = result["data"][0]  # 取第一张（也是唯一一张）
                            image_url = image_data["url"]

                            # 下载图片到本地
                            image_response = requests.get(image_url, timeout=120)  # 2分钟下载超时
                            if image_response.status_code == 200:
                                image_filename = f"tattoo_{uuid.uuid4().hex}_{i+1}.png"
                                image_path = os.path.join(self.upload_dir, image_filename)

                                with open(image_path, "wb") as f:
                                    f.write(image_response.content)

                                # 生成缩略图
                                thumbnail_path = await self._create_thumbnail(image_path)

                                # 创建带水印的显示版本和预览版本（如果不是高分辨率版本）
                                watermarked_path = None
                                preview_path = None
                                if not high_resolution:
                                    watermarked_path = await self._create_watermarked_display_image(image_path)
                                    preview_path = await self._create_watermarked_preview_image(image_path)

                                # 从实际文件获取图片尺寸
                                with Image.open(image_path) as img:
                                    width, height = img.size

                                image_info = {
                                    "image_url": f"/uploads/{image_filename}",
                                    "thumbnail_url": (
                                        f"/uploads/{os.path.basename(thumbnail_path)}" if thumbnail_path else None
                                    ),
                                    "watermarked_url": (
                                        f"/uploads/{os.path.basename(watermarked_path)}" if watermarked_path else None
                                    ),
                                    "preview_url": (
                                        f"/uploads/{os.path.basename(preview_path)}" if preview_path else None
                                    ),
                                    "width": width,
                                    "height": height,
                                    "file_size": os.path.getsize(image_path),
                                    "model": self.image_model,
                                    "quality_score": 0.9,
                                    "is_watermarked": not high_resolution,
                                    "index": i + 1,  # 图片序号
                                }
                                all_images_data.append(image_info)
                                success = True
                                break  # 成功后跳出重试循环
                            else:
                                print(f"⚠️ 下载第 {i+1} 张图片失败: {image_response.status_code}")
                                if retry == max_retries:
                                    print(f"❌ 第 {i+1} 张图片下载重试失败")
                        else:
                            print(f"⚠️ 第 {i+1} 张图片API响应无数据")
                            if retry == max_retries:
                                print(f"❌ 第 {i+1} 张图片API响应重试失败")
                    else:
                        print(f"❌ 第 {i+1} 张图片生成失败: {response.status_code} - {response.text}")
                        if retry == max_retries:
                            print(f"❌ 第 {i+1} 张图片生成重试失败")

                except Exception as e:
                    print(f"❌ 第 {i+1} 张图片生成异常: {str(e)}")
                    if retry == max_retries:
                        print(f"❌ 第 {i+1} 张图片异常重试失败")

                # 如果成功了就跳出重试循环
                if success:
                    break

                # 重试前等待一下
                if retry < max_retries:
                    import time

                    time.sleep(2)  # 等待2秒再重试

        print(f"🎉 总共成功生成了 {len(all_images_data)} 张图片")

        if not all_images_data:
            raise Exception("所有图片生成失败")

        # 如果只生成一张图片，返回单个图片信息（保持向后兼容）
        if num_images == 1:
            return all_images_data[0]
        else:
            # 多张图片时返回图片列表
            return {
                "images": all_images_data,
                "total_count": len(all_images_data),
                "requested_count": num_images,
            }

    async def _create_thumbnail(self, image_path: str) -> Optional[str]:
        """创建缩略图"""
        try:
            with Image.open(image_path) as img:
                # 确保图片是RGB模式，保持颜色
                if img.mode != "RGB":
                    img = img.convert("RGB")

                # 创建256x256的缩略图，保持高质量
                img.thumbnail((256, 256), Image.Resampling.LANCZOS)

                thumbnail_filename = f"thumb_{os.path.basename(image_path)}"
                thumbnail_path = os.path.join(self.upload_dir, thumbnail_filename)

                # 保存时指定高质量参数
                img.save(thumbnail_path, "PNG", optimize=True, quality=95)
                return thumbnail_path
        except Exception as e:
            print(f"创建缩略图失败: {str(e)}")
            return None

    async def _create_watermarked_display_image(self, image_path: str) -> Optional[str]:
        """创建带水印的显示图片（512x512）"""
        try:
            with Image.open(image_path) as img:
                # 确保图片是RGB模式
                if img.mode != "RGB":
                    img = img.convert("RGB")

                # 将图片缩放到512x512，保持高质量
                display_img = img.resize((512, 512), Image.Resampling.LANCZOS)

                # 创建水印
                watermark = Image.new("RGBA", display_img.size, (0, 0, 0, 0))
                draw = ImageDraw.Draw(watermark)

                # 水印文字
                watermark_text = "PREVIEW - 墨痕智纹"

                # 字体大小适合512x512的图片
                font_size = 20
                try:
                    font = ImageFont.truetype("arial.ttf", font_size)
                except:
                    font = ImageFont.load_default()

                # 创建全图水印网格（强力保护）
                text_bbox = draw.textbbox((0, 0), watermark_text, font=font)
                text_width = text_bbox[2] - text_bbox[0]
                text_height = text_bbox[3] - text_bbox[1]

                # 计算网格间距
                x_spacing = text_width + 40
                y_spacing = text_height + 25

                # 在整个图片上创建水印网格
                for y in range(-text_height, display_img.height + text_height, y_spacing):
                    for x in range(-text_width, display_img.width + text_width, x_spacing):
                        # 交错排列，增加覆盖密度
                        offset_x = (x_spacing // 2) if (y // y_spacing) % 2 == 1 else 0
                        actual_x = x + offset_x

                        # 绘制半透明水印
                        draw.text((actual_x, y), watermark_text, fill=(128, 128, 128, 100), font=font)

                # 合并图片和水印
                watermarked = Image.alpha_composite(display_img.convert("RGBA"), watermark)

                # 保存带水印的显示图片
                watermarked_filename = f"display_{os.path.basename(image_path)}"
                watermarked_path = os.path.join(self.upload_dir, watermarked_filename)

                # 转换为RGB并保存，保持高质量
                watermarked.convert("RGB").save(watermarked_path, "PNG", optimize=True, quality=95)
                return watermarked_path

        except Exception as e:
            print(f"创建带水印显示图片失败: {str(e)}")
            return None

    async def _create_watermarked_preview_image(self, image_path: str) -> Optional[str]:
        """创建带水印的预览图片（768x768）"""
        try:
            with Image.open(image_path) as img:
                # 确保图片是RGB模式
                if img.mode != "RGB":
                    img = img.convert("RGB")

                # 将图片缩放到768x768，保持高质量
                preview_img = img.resize((768, 768), Image.Resampling.LANCZOS)

                # 创建更明显的水印（预览版需要更强的保护）
                watermark = Image.new("RGBA", preview_img.size, (0, 0, 0, 0))
                draw = ImageDraw.Draw(watermark)

                # 水印文字
                watermark_text = "PREVIEW - 墨痕智纹"

                # 字体大小适合768x768的图片
                font_size = 28
                try:
                    font = ImageFont.truetype("arial.ttf", font_size)
                except:
                    font = ImageFont.load_default()

                # 创建全图水印网格（强力保护）
                text_bbox = draw.textbbox((0, 0), watermark_text, font=font)
                text_width = text_bbox[2] - text_bbox[0]
                text_height = text_bbox[3] - text_bbox[1]

                # 计算网格间距
                x_spacing = text_width + 50
                y_spacing = text_height + 30

                # 在整个图片上创建水印网格
                for y in range(-text_height, preview_img.height + text_height, y_spacing):
                    for x in range(-text_width, preview_img.width + text_width, x_spacing):
                        # 交错排列，增加覆盖密度
                        offset_x = (x_spacing // 2) if (y // y_spacing) % 2 == 1 else 0
                        actual_x = x + offset_x

                        # 绘制半透明水印
                        draw.text((actual_x, y), watermark_text, fill=(128, 128, 128, 80), font=font)

                # 合并图片和水印
                watermarked = Image.alpha_composite(preview_img.convert("RGBA"), watermark)

                # 保存带水印的预览图片
                preview_filename = f"preview_{os.path.basename(image_path)}"
                preview_path = os.path.join(self.upload_dir, preview_filename)

                # 转换为RGB并保存，保持高质量
                watermarked.convert("RGB").save(preview_path, "PNG", optimize=True, quality=95)
                return preview_path

        except Exception as e:
            print(f"创建带水印预览图片失败: {str(e)}")
            return None

    async def _add_watermark(self, image_path: str) -> Optional[str]:
        """添加水印"""
        try:
            with Image.open(image_path) as img:
                # 创建水印
                watermark = Image.new("RGBA", img.size, (0, 0, 0, 0))
                draw = ImageDraw.Draw(watermark)

                # 水印文字
                watermark_text = "PREVIEW - 墨痕智纹"

                # 根据图片大小调整字体大小
                font_size = max(12, min(24, img.width // 20))  # 动态字体大小
                try:
                    font = ImageFont.truetype("arial.ttf", font_size)
                except:
                    font = ImageFont.load_default()

                # 计算水印位置（右下角）
                text_bbox = draw.textbbox((0, 0), watermark_text, font=font)
                text_width = text_bbox[2] - text_bbox[0]
                text_height = text_bbox[3] - text_bbox[1]

                x = img.width - text_width - 20
                y = img.height - text_height - 20

                # 绘制半透明水印
                draw.text((x, y), watermark_text, fill=(128, 128, 128, 128), font=font)

                # 合并图片和水印
                watermarked = Image.alpha_composite(img.convert("RGBA"), watermark)

                # 保存带水印的图片
                watermarked_filename = f"watermarked_{os.path.basename(image_path)}"
                watermarked_path = os.path.join(self.upload_dir, watermarked_filename)

                # 转换为RGB并保存，保持高质量
                watermarked.convert("RGB").save(watermarked_path, "PNG", optimize=True, quality=95)
                return watermarked_path

        except Exception as e:
            print(f"添加水印失败: {str(e)}")
            return None
