<template>
  <div class="recommend-page">
    <!-- 页面头部 -->
    <section class="recommend-header">
      <div class="container">
        <div class="header-content">
          <h1>AI纹身推荐</h1>
          <p>告诉我们您的想法，AI为您推荐最合适的汉字和成语，并生成纹身设计</p>
        </div>
      </div>
    </section>

    <!-- 输入区域 -->
    <section class="input-section">
      <div class="container">
        <el-card class="input-card">
          <div class="input-form">
            <el-input
              v-model="userInput"
              type="textarea"
              :rows="4"
              placeholder="请详细描述您希望纹身表达的含义、情感或故事背景...&#10;例如：我想要一个代表坚强和勇气的纹身，能够激励我在困难时不放弃"
              maxlength="500"
              show-word-limit
              class="user-input"
            />
            <div class="input-actions">
              <el-button
                type="primary"
                size="large"
                @click="getAIRecommendations"
                :loading="isLoading"
                :disabled="!userInput.trim()"
              >
                <el-icon><MagicStick /></el-icon>
                {{ isLoading ? 'AI正在深度思考中...' : '获取AI推荐' }}
              </el-button>
            </div>
          </div>
        </el-card>
      </div>
    </section>

    <!-- AI推荐结果 -->
    <section class="recommendations-section" v-if="aiRecommendations.length > 0">
      <div class="container">
        <div class="results-header">
          <h2>AI推荐结果</h2>
          <p>{{ aiExplanation }}</p>
        </div>

        <!-- 推荐的汉字/成语选择 -->
        <div class="recommendations-grid">
          <div
            v-for="(item, index) in aiRecommendations"
            :key="index"
            class="recommendation-item"
          >
            <div class="item-content">
              <div class="item-text">{{ item.content }}</div>
              <div class="item-info">
                <div class="pinyin">{{ item.pinyin }}</div>
                <div class="meaning">{{ item.meaning }}</div>
                <div class="explanation">{{ item.explanation }}</div>
              </div>
            </div>
            <div class="item-actions">
              <el-button
                type="primary"
                @click="showBodyPartDialog(item)"
                :loading="item.generating"
                :disabled="item.generating"
              >
                <el-icon><Picture /></el-icon>
                生成纹身图
              </el-button>
            </div>

            <!-- 该项目生成的纹身图片 -->
            <div class="item-images" v-if="getLatestImagesForItem(item.content).length > 0 && item.showImages !== false">
              <div class="images-header">
                <h4>最新生成的纹身设计 (4张)</h4>
                <p>每次生成4张不同的设计图片。点击图片查看大图，满意的设计可以支付下载高清版</p>
                <div class="images-actions">
                  <el-button
                    type="info"
                    size="small"
                    @click="goToGenerationHistory"
                    style="margin-top: 10px;"
                  >
                    <el-icon><Clock /></el-icon>
                    查看图片生成历史
                  </el-button>
                  <el-button
                    type="warning"
                    size="small"
                    @click="hideImagesForItem(item.content)"
                    style="margin-top: 10px; margin-left: 10px;"
                  >
                    <el-icon><Close /></el-icon>
                    收起图片
                  </el-button>
                </div>
              </div>
              <div class="item-images-grid-4">
                <div
                  v-for="(image, index) in getLatestImagesForItem(item.content)"
                  :key="image.id"
                  class="image-item"
                >
                  <div class="image-container">
                    <!-- 显示带水印的缩略图，如果没有则显示普通缩略图 -->
                    <img :src="`http://localhost:8000${image.watermarked_url || image.thumbnail_url}`" :alt="image.content" />
                    <div class="image-overlay">
                      <div class="image-info">
                        <div class="content">{{ image.content }}</div>
                        <div class="style">{{ image.style }}</div>
                        <div class="body-part" v-if="image.bodyPart">{{ getBodyPartLabel(image.bodyPart) }}</div>
                        <div class="image-index">第{{ index + 1 }}张</div>
                      </div>
                      <div class="image-actions">
                        <el-button size="small" @click="previewImage(image)">
                          <el-icon><View /></el-icon>
                          预览
                        </el-button>
                        <el-button
                          type="primary"
                          size="small"
                          @click="purchaseHighRes(image)"
                          v-if="image.is_watermarked"
                        >
                          <el-icon><Download /></el-icon>
                          购买高清
                        </el-button>
                        <el-button
                          type="success"
                          size="small"
                          @click="downloadImage(image)"
                          v-else
                        >
                          <el-icon><Download /></el-icon>
                          下载
                        </el-button>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- 显示图片按钮（当图片被隐藏时） -->
            <div class="show-images-section" v-if="getLatestImagesForItem(item.content).length > 0 && item.showImages === false">
              <el-button
                type="info"
                @click="showImagesForItem(item.content)"
                style="margin-top: 16px;"
              >
                <el-icon><Picture /></el-icon>
                查看最新生成的图片 (4张)
              </el-button>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- 身体部位选择对话框 -->
    <el-dialog
      v-model="bodyPartDialogVisible"
      title="选择纹身部位"
      width="500px"
      :before-close="handleDialogClose"
    >
      <div class="body-part-selection">
        <div class="selection-header">
          <h3>为 "{{ selectedItem?.content }}" 选择纹身部位</h3>
          <p>不同部位的纹身设计会有所不同，请选择您希望纹身的身体部位：</p>
        </div>

        <div class="body-parts-grid">
          <div
            v-for="part in bodyParts"
            :key="part.value"
            class="body-part-item"
            :class="{ active: selectedBodyPart === part.value }"
            @click="selectedBodyPart = part.value"
          >
            <div class="part-icon">{{ part.icon }}</div>
            <div class="part-name">{{ part.label }}</div>
            <div class="part-desc">{{ part.description }}</div>
          </div>
        </div>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="bodyPartDialogVisible = false">取消</el-button>
          <el-button
            type="primary"
            @click="confirmBodyPartAndGenerate"
            :disabled="!selectedBodyPart"
          >
            确认生成
          </el-button>
        </div>
      </template>
    </el-dialog>

  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { MagicStick, Picture, View, Download, Close, Clock } from '@element-plus/icons-vue'
import { useUserStore } from '@/stores/counter'
import { CharacterService } from '@/services/character'
import { TattooService } from '@/services/tattoo'

const router = useRouter()
const userStore = useUserStore()

// 响应式数据
const userInput = ref('')
const isLoading = ref(false)
const aiRecommendations = ref<any[]>([])
const aiExplanation = ref('')
const tattooImages = ref<any[]>([])

// 身体部位选择相关
const bodyPartDialogVisible = ref(false)
const selectedItem = ref<any>(null)
const selectedBodyPart = ref('')

// 身体部位选项
const bodyParts = ref([
  {
    value: 'forearm',
    label: '前臂',
    icon: '💪',
    description: '经典位置，展示效果好'
  },
  {
    value: 'shoulder',
    label: '肩膀',
    icon: '🫸',
    description: '适合较大设计'
  },
  {
    value: 'back',
    label: '后背',
    icon: '🫱',
    description: '空间充足，适合复杂图案'
  },
  {
    value: 'chest',
    label: '胸部',
    icon: '🫶',
    description: '贴近心脏，寓意深刻'
  },
  {
    value: 'wrist',
    label: '手腕',
    icon: '⌚',
    description: '小巧精致，日常可见'
  },
  {
    value: 'ankle',
    label: '脚踝',
    icon: '🦶',
    description: '低调优雅，可隐可现'
  }
])


onMounted(() => {
  // 检查用户登录状态
  if (!userStore.isAuthenticated) {
    ElMessage.warning('请先登录')
    router.push('/login')
  }
})

// 获取AI推荐
const getAIRecommendations = async () => {
  if (!userInput.value.trim()) return

  isLoading.value = true

  // 显示友好的加载提示
  ElMessage.info({
    message: 'AI正在深度思考中，请耐心等待...',
    duration: 3000
  })

  try {
    // 调用AI推荐API
    const result = await CharacterService.getRecommendations({
      keywords: [],
      description: userInput.value,
      type: 'both',
      max_results: 5
    })

    // 处理推荐结果
    aiRecommendations.value = [
      ...result.characters.map((char: any) => ({
        content: char.character,
        pinyin: char.pinyin,
        meaning: char.meaning_en,
        explanation: char.explanation,
        type: 'character',
        generating: false,
        showImages: true  // 默认显示图片
      })),
      ...result.words.map((word: any) => ({
        content: word.word,
        pinyin: word.pinyin,
        meaning: word.meaning_en,
        explanation: word.explanation,
        type: 'word',
        generating: false,
        showImages: true  // 默认显示图片
      })),
      ...result.idioms.map((idiom: any) => ({
        content: idiom.idiom,
        pinyin: idiom.pinyin,
        meaning: idiom.meaning_en,
        explanation: idiom.explanation,
        type: 'idiom',
        generating: false,
        showImages: true  // 默认显示图片
      }))
    ]

    aiExplanation.value = result.ai_explanation

    ElMessage.success('AI推荐获取成功！')

  } catch (error) {
    console.error('获取推荐失败:', error)
    ElMessage.error('获取推荐失败，请稍后重试')
  } finally {
    isLoading.value = false
  }
}

// 显示身体部位选择对话框
const showBodyPartDialog = (item: any) => {
  selectedItem.value = item
  selectedBodyPart.value = ''
  bodyPartDialogVisible.value = true
}

// 关闭对话框
const handleDialogClose = () => {
  bodyPartDialogVisible.value = false
  selectedItem.value = null
  selectedBodyPart.value = ''
}

// 确认身体部位并生成纹身
const confirmBodyPartAndGenerate = async () => {
  if (!selectedBodyPart.value || !selectedItem.value) return

  bodyPartDialogVisible.value = false
  await generateTattooDesign(selectedItem.value, selectedBodyPart.value)
}

// 生成纹身设计
const generateTattooDesign = async (item: any, bodyPart: string = 'forearm') => {
  item.generating = true

  // 显示图像生成专用提示
  ElMessage.info({
    message: 'AI正在创作4张专属纹身设计，这可能需要几分钟时间，请耐心等待...',
    duration: 5000
  })

  try {
    // 调用纹身生成API（现在返回4张图片）
    const result = await TattooService.generateTattooImage({
      content: item.content,
      style: 'calligraphy',
      position: bodyPart,
      high_resolution: false
    })

    // 处理多张图片结果
    if (result.images && result.images.length > 0) {
      // 为每张图片添加额外信息
      const newImages = result.images.map((img: any) => ({
        ...img,
        content: item.content,
        style: '书法体',
        bodyPart: bodyPart,
        is_watermarked: true,
        generatedAt: new Date().toISOString(),
        generationBatch: Date.now() // 添加批次标识，用于区分不同次的生成
      }))

      // 移除该汉字的旧图片，用新的4张图片替换
      const otherImages = tattooImages.value.filter(img => img.content !== item.content)

      // 新图片直接替换旧图片
      tattooImages.value = [
        ...otherImages,
        ...newImages
      ]

      ElMessage.success(`成功生成${result.images.length}张全新的纹身设计！`)
    } else {
      ElMessage.warning('未生成任何图片，请重试')
    }

  } catch (error) {
    console.error('生成纹身设计失败:', error)
    ElMessage.error('生成失败，请稍后重试')
  } finally {
    item.generating = false
  }
}

// 获取特定内容的图片（所有历史图片）
const getImagesForItem = (content: string) => {
  return tattooImages.value.filter(image => image.content === content)
}

// 获取特定内容的最新4张图片
const getLatestImagesForItem = (content: string) => {
  const images = tattooImages.value.filter(image => image.content === content)

  // 如果有批次标识，按批次分组并取最新批次
  if (images.length > 0 && images[0].generationBatch) {
    // 按批次分组
    const batches = images.reduce((acc: any, img: any) => {
      const batch = img.generationBatch
      if (!acc[batch]) acc[batch] = []
      acc[batch].push(img)
      return acc
    }, {})

    // 获取最新批次
    const latestBatch = Math.max(...Object.keys(batches).map(Number))
    return batches[latestBatch] || []
  }

  // 如果没有批次标识，返回最新的4张（按生成时间排序）
  return images
    .sort((a: any, b: any) => new Date(b.generatedAt || 0).getTime() - new Date(a.generatedAt || 0).getTime())
    .slice(0, 4)
}

// 预览图片（预览时也显示带水印版本，保护版权）
const previewImage = (image: any) => {
  // 如果已购买（无水印），显示原图
  if (!image.is_watermarked) {
    window.open(`http://localhost:8000${image.image_url}`, '_blank')
  } else {
    // 未购买时，预览显示768x768带水印版本（比页面显示的512x512大一些）
    window.open(`http://localhost:8000${image.preview_url || image.watermarked_url || image.thumbnail_url}`, '_blank')
  }
}

// 购买高清版
const purchaseHighRes = async (image: any) => {
  try {
    const result = await TattooService.unlockHighResolution(image.id)

    // 更新图片信息
    const index = tattooImages.value.findIndex(img => img.id === image.id)
    if (index !== -1) {
      tattooImages.value[index] = { ...result, is_watermarked: false }
    }

    ElMessage.success('高清版本解锁成功！')

  } catch (error) {
    console.error('购买高清版失败:', error)
    ElMessage.error('购买失败，请稍后重试')
  }
}

// 下载图片
const downloadImage = (image: any) => {
  const link = document.createElement('a')
  link.href = `http://localhost:8000${image.image_url}`
  link.download = `tattoo-${image.content}-${Date.now()}.png`
  link.click()
}

// 隐藏指定汉字的图片
const hideImagesForItem = (content: string) => {
  const item = aiRecommendations.value.find(rec => rec.content === content)
  if (item) {
    item.showImages = false
  }
}

// 显示指定汉字的图片
const showImagesForItem = (content: string) => {
  const item = aiRecommendations.value.find(rec => rec.content === content)
  if (item) {
    item.showImages = true
  }
}

// 获取身体部位的中文标签
const getBodyPartLabel = (bodyPartValue: string) => {
  const part = bodyParts.value.find(p => p.value === bodyPartValue)
  return part ? part.label : bodyPartValue
}

// 跳转到图片生成历史页面
const goToGenerationHistory = () => {
  router.push('/profile?tab=designs')
}


</script>

<style scoped>
.recommend-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
}

/* 页面头部 */
.recommend-header {
  padding: 60px 0 40px;
  background: linear-gradient(135deg, rgba(212, 175, 55, 0.1) 0%, rgba(255, 215, 0, 0.05) 100%);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 24px;
}

.header-content {
  text-align: center;
}

.header-content h1 {
  font-size: 2.5rem;
  font-weight: 600;
  color: #ffffff;
  margin: 0 0 16px 0;
  font-family: 'SimSun', serif;
}

.header-content p {
  font-size: 1.1rem;
  color: rgba(255, 255, 255, 0.7);
  max-width: 600px;
  margin: 0 auto;
}

/* 输入区域 */
.input-section {
  padding: 40px 0;
}

.input-card {
  max-width: 800px;
  margin: 0 auto;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
}

.input-form {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.user-input {
  font-size: 1.1rem;
}

:deep(.user-input .el-textarea__inner) {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.2);
  color: #ffffff;
  font-size: 1.1rem;
  line-height: 1.6;
}

.input-actions {
  text-align: center;
}

/* 推荐结果区域 */
.recommendations-section {
  padding: 40px 0;
}

.results-header {
  text-align: center;
  margin-bottom: 40px;
}

.results-header h2 {
  font-size: 2rem;
  font-weight: 600;
  color: #ffffff;
  margin: 0 0 16px 0;
}

.results-header p {
  font-size: 1.1rem;
  color: rgba(255, 255, 255, 0.8);
  line-height: 1.6;
  max-width: 800px;
  margin: 0 auto;
  background: rgba(255, 215, 0, 0.1);
  padding: 20px;
  border-radius: 12px;
  border: 1px solid rgba(255, 215, 0, 0.2);
}

.recommendations-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 24px;
}

.recommendation-item {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 16px;
  padding: 24px;
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
}

.recommendation-item:hover {
  transform: translateY(-5px);
  background: rgba(255, 255, 255, 0.08);
  border-color: rgba(255, 215, 0, 0.3);
}

.item-text {
  font-size: 3rem;
  font-weight: bold;
  color: #ffd700;
  font-family: 'SimSun', serif;
  text-align: center;
  margin-bottom: 16px;
  text-shadow: 0 0 20px rgba(255, 215, 0, 0.3);
}

.item-info {
  text-align: center;
  margin-bottom: 20px;
}

.pinyin {
  font-size: 1.1rem;
  color: rgba(255, 255, 255, 0.8);
  margin-bottom: 8px;
  font-style: italic;
}

.meaning {
  font-size: 1rem;
  color: rgba(255, 255, 255, 0.9);
  margin-bottom: 12px;
  font-weight: 500;
}

.explanation {
  font-size: 0.9rem;
  color: rgba(255, 255, 255, 0.7);
  line-height: 1.5;
}

.item-actions {
  text-align: center;
}

/* 推荐项目下的图片区域 */
.item-images {
  margin-top: 24px;
  padding-top: 24px;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.images-header {
  text-align: center;
  margin-bottom: 20px;
}

.images-header h4 {
  font-size: 1.2rem;
  font-weight: 600;
  color: #ffd700;
  margin: 0 0 8px 0;
}

.images-header p {
  font-size: 0.9rem;
  color: rgba(255, 255, 255, 0.6);
  margin: 0;
}

.images-actions {
  text-align: center;
}

.show-images-section {
  text-align: center;
  margin-top: 16px;
}

.body-part {
  font-size: 0.8rem;
  color: rgba(255, 255, 255, 0.6);
  margin-top: 4px;
}

.item-images-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16px;
}

/* 4张图片专用网格布局 */
.item-images-grid-4 {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16px;
}

/* 在大屏幕上显示为2x2网格 */
@media (min-width: 768px) {
  .item-images-grid-4 {
    grid-template-columns: repeat(2, 1fr);
  }
}

/* 在小屏幕上显示为1列 */
@media (max-width: 767px) {
  .item-images-grid-4 {
    grid-template-columns: 1fr;
  }
}

/* 纹身图片画廊 */
.tattoo-gallery {
  padding: 60px 0;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.gallery-header {
  text-align: center;
  margin-bottom: 40px;
}

.gallery-header h2 {
  font-size: 2rem;
  font-weight: 600;
  color: #ffffff;
  margin: 0 0 16px 0;
}

.gallery-header p {
  font-size: 1rem;
  color: rgba(255, 255, 255, 0.7);
}

.images-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 20px;
}

.image-item {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  overflow: hidden;
  cursor: pointer;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
}

.image-item:hover {
  transform: translateY(-2px);
  border-color: rgba(255, 215, 0, 0.3);
}

.image-container {
  position: relative;
  width: 100%;
  height: 180px;
  overflow: hidden;
}

.image-container img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.image-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(to bottom, transparent 0%, rgba(0, 0, 0, 0.8) 100%);
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  padding: 12px;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.image-item:hover .image-overlay {
  opacity: 1;
}

.image-info {
  color: #ffffff;
}

.image-info .content {
  font-size: 1.2rem;
  font-weight: 600;
  font-family: 'SimSun', serif;
  margin-bottom: 4px;
}

.image-info .style {
  font-size: 0.9rem;
  color: rgba(255, 255, 255, 0.8);
}

.image-info .image-index {
  font-size: 0.8rem;
  color: rgba(255, 215, 0, 0.9);
  font-weight: 500;
  margin-top: 4px;
}

.image-actions {
  display: flex;
  gap: 8px;
  justify-content: center;
}



/* 身体部位选择对话框 */
.body-part-selection {
  padding: 20px 0;
}

.selection-header {
  text-align: center;
  margin-bottom: 30px;
}

.selection-header h3 {
  font-size: 1.3rem;
  font-weight: 600;
  color: #333;
  margin: 0 0 12px 0;
  font-family: 'SimSun', serif;
}

.selection-header p {
  font-size: 0.95rem;
  color: #666;
  line-height: 1.5;
  margin: 0;
}

.body-parts-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16px;
}

.body-part-item {
  padding: 20px 16px;
  border: 2px solid #e5e5e5;
  border-radius: 12px;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
  background: #fafafa;
}

.body-part-item:hover {
  border-color: #d4af37;
  background: #f9f7f0;
  transform: translateY(-2px);
}

.body-part-item.active {
  border-color: #d4af37;
  background: linear-gradient(135deg, #ffd700 0%, #d4af37 100%);
  color: white;
}

.part-icon {
  font-size: 2rem;
  margin-bottom: 8px;
}

.part-name {
  font-size: 1.1rem;
  font-weight: 600;
  margin-bottom: 6px;
  font-family: 'SimSun', serif;
}

.part-desc {
  font-size: 0.85rem;
  opacity: 0.8;
  line-height: 1.3;
}

.body-part-item.active .part-desc {
  opacity: 0.9;
}

/* 表单样式覆盖 */
:deep(.el-card__body) {
  background: transparent;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .header-content h1 {
    font-size: 2rem;
  }

  .recommendations-grid {
    grid-template-columns: 1fr;
  }

  .images-grid {
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  }

  .item-text {
    font-size: 2.5rem;
  }

  .input-card {
    margin: 0 16px;
  }

  .item-images-grid {
    grid-template-columns: 1fr;
  }

  .image-container {
    height: 160px;
  }
}
</style>
