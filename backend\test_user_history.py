#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试用户纹身历史API
"""

import asyncio
import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from sqlalchemy.orm import Session
from app.db.database import get_db
from app.models.tattoo import Tattoo<PERSON>mage, TattooRequest
from app.models.user import User


def test_user_history_query():
    """测试用户历史查询"""
    
    print("🧪 测试用户纹身历史查询...")
    print("=" * 60)
    
    try:
        # 获取数据库会话
        db = next(get_db())
        
        # 查找一个用户
        user = db.query(User).first()
        if not user:
            print("❌ 没有找到用户")
            return False
            
        print(f"✅ 找到用户: {user.username}")
        
        # 查询该用户的纹身图片历史
        tattoo_images = (
            db.query(TattooImage)
            .join(TattooRequest)
            .filter(TattooRequest.user_id == user.id)
            .order_by(TattooImage.created_at.desc())
            .limit(10)
            .all()
        )
        
        print(f"📊 查询结果:")
        print(f"   用户ID: {user.id}")
        print(f"   纹身图片数量: {len(tattoo_images)}")
        
        if tattoo_images:
            print(f"   最新图片:")
            for i, img in enumerate(tattoo_images[:3], 1):
                print(f"     {i}. ID: {img.id}, 内容: {img.content}, 创建时间: {img.created_at}")
            return True
        else:
            print("   没有找到纹身图片")
            
            # 检查是否有纹身请求
            requests = db.query(TattooRequest).filter(TattooRequest.user_id == user.id).all()
            print(f"   纹身请求数量: {len(requests)}")
            
            # 检查是否有任何纹身图片
            all_images = db.query(TattooImage).all()
            print(f"   系统中总图片数量: {len(all_images)}")
            
            return len(all_images) == 0  # 如果系统中没有图片，这是正常的
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        print("详细错误信息:")
        traceback.print_exc()
        return False
    finally:
        db.close()


def main():
    """主函数"""
    
    print("🧪 用户纹身历史API测试")
    print("=" * 60)
    
    success = test_user_history_query()
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 用户历史查询测试通过！")
        print("💡 API应该能正常返回用户的纹身历史")
    else:
        print("❌ 用户历史查询测试失败")
        print("💡 请检查数据库连接和表结构")
    print("=" * 60)


if __name__ == "__main__":
    main()
