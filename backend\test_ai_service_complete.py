#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
完整的AI服务测试
测试文本AI模型的系统提示词是否能正确要求模型推荐指定数量的汉字或成语
"""

import asyncio
import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.services.ai_service_new import AIService


async def test_ai_recommendations():
    """测试AI推荐功能"""
    
    print("🚀 开始测试AI推荐功能...")
    
    try:
        # 创建AI服务实例
        ai_service = AIService()
        print("✅ AI服务实例化成功")
        
        # 测试参数
        keywords = ["勇气", "智慧"]
        description = "我想要一个代表勇气和智慧的纹身"
        max_results = 2
        
        print(f"📝 测试参数:")
        print(f"   关键词: {keywords}")
        print(f"   描述: {description}")
        print(f"   推荐数量: {max_results}")
        
        # 调用AI推荐
        print("\n🤖 调用AI推荐服务...")
        recommendations = await ai_service.get_recommendations(
            keywords=keywords,
            description=description,
            recommendation_type="both",
            max_results=max_results
        )
        
        print("✅ AI推荐调用成功！")
        
        # 验证结果
        print(f"\n📊 推荐结果验证:")
        print(f"   汉字数量: {len(recommendations.characters)}")
        print(f"   词语数量: {len(recommendations.words)}")
        print(f"   成语数量: {len(recommendations.idioms)}")
        
        # 检查数量是否正确
        expected_count = max_results
        
        if len(recommendations.characters) == expected_count:
            print(f"✅ 汉字数量正确: {len(recommendations.characters)}/{expected_count}")
        else:
            print(f"❌ 汉字数量错误: {len(recommendations.characters)}/{expected_count}")
            
        if len(recommendations.words) == expected_count:
            print(f"✅ 词语数量正确: {len(recommendations.words)}/{expected_count}")
        else:
            print(f"❌ 词语数量错误: {len(recommendations.words)}/{expected_count}")
            
        if len(recommendations.idioms) == expected_count:
            print(f"✅ 成语数量正确: {len(recommendations.idioms)}/{expected_count}")
        else:
            print(f"❌ 成语数量错误: {len(recommendations.idioms)}/{expected_count}")
        
        # 显示推荐内容
        print(f"\n🎯 AI解释:")
        print(f"   {recommendations.ai_explanation}")
        
        print(f"\n📝 汉字推荐:")
        for i, char in enumerate(recommendations.characters, 1):
            print(f"   {i}. {char.character} ({char.pinyin})")
            print(f"      含义: {char.meaning_zh}")
            print(f"      评分: {char.score}")
            
        print(f"\n📝 词语推荐:")
        for i, word in enumerate(recommendations.words, 1):
            print(f"   {i}. {word.word} ({word.pinyin})")
            print(f"      含义: {word.meaning_zh}")
            print(f"      评分: {word.score}")
        
        print(f"\n📚 成语推荐:")
        for i, idiom in enumerate(recommendations.idioms, 1):
            print(f"   {i}. {idiom.idiom} ({idiom.pinyin})")
            print(f"      含义: {idiom.meaning_zh}")
            print(f"      评分: {idiom.score}")
        
        # 总结
        total_expected = expected_count * 3  # 汉字 + 词语 + 成语
        total_actual = len(recommendations.characters) + len(recommendations.words) + len(recommendations.idioms)
        
        print(f"\n📊 总结:")
        print(f"   期望总数: {total_expected} (每类{expected_count}个)")
        print(f"   实际总数: {total_actual}")
        
        if total_actual == total_expected:
            print("🎉 测试成功！AI模型正确按照指定数量推荐了汉字、词语和成语")
            return True
        else:
            print("❌ 测试失败！AI模型没有按照指定数量推荐")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        print("详细错误信息:")
        traceback.print_exc()
        return False


if __name__ == "__main__":
    print("=" * 60)
    print("🧪 AI服务完整功能测试")
    print("=" * 60)
    
    success = asyncio.run(test_ai_recommendations())
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 所有测试通过！")
    else:
        print("❌ 测试失败，请检查配置和代码")
    print("=" * 60)
