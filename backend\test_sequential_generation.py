#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试连续生成多张图片功能
"""

import asyncio
import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.services.image_generation_service import ImageGenerationService


async def test_sequential_generation():
    """测试连续生成多张图片"""
    
    print("🧪 测试连续生成多张图片功能...")
    print("=" * 60)
    
    try:
        # 创建图像生成服务
        image_service = ImageGenerationService()
        print("✅ 图像生成服务实例化成功")
        
        # 测试参数
        prompt = "请为我生成文字'智'的前臂纹身图，艺术字体，适合纹身，文化传统，精美作品，最佳质量，超高细节，清晰锐利"
        num_images = 2  # 先测试2张，避免等待太久
        
        print(f"📝 测试参数:")
        print(f"   提示词: {prompt}")
        print(f"   图片数量: {num_images}")
        
        # 调用图像生成
        print(f"\n🎨 开始连续生成图片...")
        result = await image_service.generate_tattoo_image(
            prompt=prompt,
            high_resolution=False,
            num_images=num_images
        )
        
        print("✅ 图像生成完成！")
        
        # 验证结果
        print(f"\n📊 生成结果验证:")
        
        if "images" in result:
            # 多张图片模式
            print(f"   模式: 多张图片")
            print(f"   生成数量: {len(result['images'])}")
            print(f"   请求数量: {result.get('requested_count', 'N/A')}")
            print(f"   总数: {result.get('total_count', 'N/A')}")
            
            # 显示每张图片的信息
            for i, img in enumerate(result['images'], 1):
                print(f"   图片{i}:")
                print(f"     URL: {img.get('image_url', 'N/A')}")
                print(f"     尺寸: {img.get('width', 'N/A')}x{img.get('height', 'N/A')}")
                print(f"     序号: {img.get('index', 'N/A')}")
                
            if len(result['images']) == num_images:
                print(f"✅ 图片数量正确: {len(result['images'])}/{num_images}")
                return True
            else:
                print(f"⚠️ 图片数量不完全匹配: {len(result['images'])}/{num_images}")
                return len(result['images']) > 0  # 只要有图片生成就算部分成功
        else:
            # 单张图片模式
            print(f"   模式: 单张图片")
            print(f"   图片URL: {result.get('image_url', 'N/A')}")
            
            if num_images == 1:
                print(f"✅ 单张图片模式正确")
                return True
            else:
                print(f"❌ 期望{num_images}张图片，但返回单张图片模式")
                return False
                
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        print("详细错误信息:")
        traceback.print_exc()
        return False


async def main():
    """主函数"""
    
    print("🧪 连续生成多张图片测试")
    print("=" * 60)
    
    success = await test_sequential_generation()
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 连续生成测试通过！")
        print("💡 现在可以生成多张图片了")
    else:
        print("❌ 连续生成测试失败")
    print("=" * 60)


if __name__ == "__main__":
    asyncio.run(main())
