#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单的API测试
"""

import asyncio
import httpx
import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.core.config import settings


async def test_simple_api():
    """测试简单的API调用"""
    
    print("🧪 测试简单的API调用...")
    
    headers = {
        "Authorization": f"Bearer {settings.TEXT_AI_API_KEY}",
        "Content-Type": "application/json"
    }
    
    # 非常简单的测试请求
    test_data = {
        "model": settings.TEXT_AI_MODEL,
        "messages": [
            {"role": "user", "content": "请回复：测试成功"}
        ],
        "temperature": 0.1,
        "max_tokens": 50
    }
    
    try:
        print(f"📡 发送请求到: {settings.TEXT_AI_API_URL}/chat/completions")
        
        async with httpx.AsyncClient(timeout=30.0) as client:
            response = await client.post(
                f"{settings.TEXT_AI_API_URL}/chat/completions",
                headers=headers,
                json=test_data
            )
            
            print(f"📊 响应状态码: {response.status_code}")
            
            if response.status_code == 200:
                result = response.json()
                content = result.get("choices", [{}])[0].get("message", {}).get("content", "")
                print(f"🤖 AI响应: {content}")
                print("✅ 简单API测试成功！")
                return True
            else:
                print(f"❌ API错误: {response.status_code}")
                print(f"错误内容: {response.text}")
                return False
                
    except Exception as e:
        print(f"❌ 请求异常: {e}")
        return False


if __name__ == "__main__":
    success = asyncio.run(test_simple_api())
    if success:
        print("🎉 测试通过！")
    else:
        print("💥 测试失败！")
