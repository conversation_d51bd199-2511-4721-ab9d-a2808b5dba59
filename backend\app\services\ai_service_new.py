"""
AI服务 - 使用DeepSeek
AI Service using DeepSeek for character recommendations
"""

from typing import List, Optional, Dict, Any
from app.schemas.character import (
    RecommendationResponse,
    CharacterRecommendation,
    WordRecommendation,
    IdiomRecommendation,
)
from app.services.text_ai_service import TextAIService


class AIService:
    """AI服务类"""

    def __init__(self):
        self.text_ai_service = TextAIService()

    async def get_recommendations(
        self,
        keywords: List[str],
        description: Optional[str] = None,
        recommendation_type: str = "both",
        max_results: int = 5,
        db=None,
    ) -> RecommendationResponse:
        """
        获取AI推荐的汉字和成语
        """

        # 构建用户描述
        user_description = self._build_user_description(keywords, description)

        try:
            # 调用文本AI API
            ai_data = await self.text_ai_service.recommend_characters(user_description, max_results)

            # 验证和丰富推荐结果
            recommendations = self._enrich_recommendations(ai_data)

            return recommendations

        except Exception as e:
            error_msg = f"AI推荐失败: {type(e).__name__}: {e}"
            print(error_msg)
            # 直接抛出异常，不使用备用方案
            raise Exception(f"AI推荐服务失败: {error_msg}")

    def _build_user_description(self, keywords: List[str], description: Optional[str]) -> str:
        """构建用户描述"""

        parts = []

        if keywords:
            keywords_str = ", ".join(keywords)
            parts.append(f"关键词：{keywords_str}")

        if description:
            parts.append(f"详细描述：{description}")

        if not parts:
            return "请推荐一些适合纹身的汉字和成语"

        return " | ".join(parts)

    def _enrich_recommendations(self, ai_data: Dict[str, Any]) -> RecommendationResponse:
        """验证和丰富推荐结果"""

        characters = []
        words = []
        idioms = []

        # 处理汉字推荐
        if "characters" in ai_data:
            for char_data in ai_data["characters"]:
                try:
                    character_rec = CharacterRecommendation(
                        character=char_data.get("character", ""),
                        pinyin=char_data.get("pinyin", ""),
                        meaning_en=char_data.get("meaning_en", ""),
                        meaning_zh=char_data.get("meaning_zh", ""),
                        cultural_context=char_data.get("cultural_context"),
                        score=float(char_data.get("score", 0.5)),
                        explanation=char_data.get("explanation", ""),
                    )
                    characters.append(character_rec)
                except Exception as e:
                    print(f"处理汉字推荐失败: {e}")
                    continue

        # 处理词语推荐
        if "words" in ai_data:
            for word_data in ai_data["words"]:
                try:
                    word_rec = WordRecommendation(
                        word=word_data.get("word", ""),
                        pinyin=word_data.get("pinyin", ""),
                        meaning_en=word_data.get("meaning_en", ""),
                        meaning_zh=word_data.get("meaning_zh", ""),
                        cultural_context=word_data.get("cultural_context"),
                        score=float(word_data.get("score", 0.5)),
                        explanation=word_data.get("explanation", ""),
                    )
                    words.append(word_rec)
                except Exception as e:
                    print(f"处理词语推荐失败: {e}")
                    continue

        # 处理成语推荐
        if "idioms" in ai_data:
            for idiom_data in ai_data["idioms"]:
                try:
                    idiom_rec = IdiomRecommendation(
                        idiom=idiom_data.get("idiom", ""),
                        pinyin=idiom_data.get("pinyin", ""),
                        meaning_en=idiom_data.get("meaning_en", ""),
                        meaning_zh=idiom_data.get("meaning_zh", ""),
                        origin_story=idiom_data.get("origin_story"),
                        score=float(idiom_data.get("score", 0.5)),
                        explanation=idiom_data.get("explanation", ""),
                    )
                    idioms.append(idiom_rec)
                except Exception as e:
                    print(f"处理成语推荐失败: {e}")
                    continue

        ai_explanation = ai_data.get("ai_explanation", "基于您的需求，我为您推荐了以下汉字、词语和成语。")

        return RecommendationResponse(characters=characters, words=words, idioms=idioms, ai_explanation=ai_explanation)
