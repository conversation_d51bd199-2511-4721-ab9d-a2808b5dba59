#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
文本AI服务 - 可配置的AI模型服务
支持DeepSeek、OpenAI等多种文本AI模型
"""

import json
import httpx
from typing import Dict, Any
from app.core.config import settings


class TextAIService:
    """文本AI服务类 - 可配置不同的AI模型"""

    def __init__(self):
        self.api_key = settings.TEXT_AI_API_KEY
        self.base_url = settings.TEXT_AI_API_URL
        self.model = settings.TEXT_AI_MODEL

    async def recommend_characters(self, user_description: str, max_results: int = 5) -> Dict[str, Any]:
        """
        根据用户描述推荐汉字和成语

        Args:
            user_description: 用户的描述和想法
            max_results: 最大推荐数量

        Returns:
            包含推荐结果的字典
        """

        # 构建提示词
        prompt = self._build_recommendation_prompt(user_description, max_results)

        try:
            async with httpx.AsyncClient() as client:
                response = await client.post(
                    f"{self.base_url}/chat/completions",
                    headers={"Authorization": f"Bearer {self.api_key}", "Content-Type": "application/json"},
                    json={
                        "model": self.model,
                        "messages": [
                            {"role": "system", "content": self._get_system_prompt()},
                            {"role": "user", "content": prompt},
                        ],
                        "temperature": 0.7,
                        "max_tokens": 3000,
                        "stream": False,
                    },
                    timeout=180.0,  # DeepSeek V3是思维型模型，需要更长时间
                )

                if response.status_code == 200:
                    result = response.json()
                    content = result["choices"][0]["message"]["content"]

                    # 解析AI返回的JSON结果
                    try:
                        # 清理markdown格式
                        clean_content = content.strip()
                        if clean_content.startswith("```json"):
                            clean_content = clean_content[7:]
                        if clean_content.endswith("```"):
                            clean_content = clean_content[:-3]
                        clean_content = clean_content.strip()

                        recommendations = json.loads(clean_content)
                        # 验证推荐数量是否正确
                        if self._validate_recommendations(recommendations, max_results):
                            return recommendations
                        else:
                            print(f"AI返回的推荐数量不正确，期望{max_results}个汉字和成语")
                            raise ValueError(f"AI返回的推荐数量不正确，期望{max_results}个汉字和成语")
                    except json.JSONDecodeError as e:
                        # 如果JSON解析失败，抛出异常
                        print(f"AI返回的JSON格式无效: {e}")
                        print(f"原始内容: {content[:200]}...")
                        raise ValueError(f"AI返回的JSON格式无效: {e}")

                else:
                    error_msg = f"文本AI API错误: {response.status_code} - {response.text}"
                    print(error_msg)
                    raise Exception(error_msg)

        except httpx.TimeoutException as e:
            error_msg = f"文本AI API超时: {e}"
            print(error_msg)
            raise Exception(error_msg)
        except httpx.RequestError as e:
            error_msg = f"文本AI API请求错误: {e}"
            print(error_msg)
            raise Exception(error_msg)
        except Exception as e:
            error_msg = f"调用文本AI API失败: {type(e).__name__}: {e}"
            print(error_msg)
            raise Exception(error_msg)

    def _get_system_prompt(self) -> str:
        """获取系统提示词"""
        return """你是一个专业的汉字文化专家和纹身设计顾问，具有以下专业能力：

1. **汉字文化专家**：深度理解汉字的文化内涵、历史典故和寓意
2. **成语专家**：熟悉成语的来源、含义和文化背景
3. **纹身设计顾问**：了解哪些汉字和成语适合作为纹身设计

**你的任务**：
- 根据用户的描述和情感需求，推荐最合适的汉字、词语和成语
- 必须严格按照用户要求的数量进行推荐
- 每个推荐都要提供详细的文化解释和推荐理由
- 确保推荐的内容适合纹身设计（字形美观、寓意积极）

**推荐原则**：
1. **文化准确性**：确保汉字、词语和成语的含义、读音、文化背景准确无误
2. **情感匹配度**：推荐内容要与用户的情感需求高度匹配
3. **纹身适用性**：考虑字形美观度、笔画复杂度、视觉效果
4. **积极寓意**：只推荐具有正面、积极寓意的汉字、词语和成语
5. **文化深度**：优先推荐有深厚文化底蕴和典故的内容

**输出要求**：
- 必须严格按照JSON格式输出，不要添加任何其他文字
- 推荐数量必须与用户要求完全一致
- 每个推荐都要包含完整的信息字段
- 评分要客观反映推荐的匹配度（1-10分）"""

    def _build_recommendation_prompt(self, user_description: str, max_results: int) -> str:
        """构建推荐提示词"""

        prompt = f"""
**任务要求**：
根据用户描述，推荐最适合的汉字和成语用于纹身设计。

**用户描述**："{user_description}"

**明确要求**：
- 必须推荐 {max_results} 个汉字
- 必须推荐 {max_results} 个词语
- 必须推荐 {max_results} 个成语
- 数量不能多也不能少，严格按照要求

**汉字推荐标准**：
1. 单个汉字，寓意深刻且与用户需求匹配
2. 字形美观，适合纹身设计
3. 具有积极正面的文化内涵
4. 笔画适中，不过于复杂
5. 有深厚的文化底蕴

**词语推荐标准**：
1. 两个汉字，准确传达用户期望的核心意义，且含义积极美好。
2. 音韵和谐：读音悦耳、顺口，易于念诵。
3. 凝练有力：用最少的字表达清晰、有分量的含义。
4. 常用易懂：优先选择大众熟知、易于理解和产生共鸣的词汇。
5. 富有美感/文化感：字形组合相对协调，或词语本身能引发美好的联想、具有一定的文化气息。

**成语推荐标准**：
1. 优先推荐4字成语
2. 朗朗上口，寓意深远
3. 与用户情感需求高度相关
4. 有明确的典故来源
5. 适合作为人生格言或座右铭

**重要提醒**：
- 汉字数量：必须是 {max_results} 个
- 词语数量：必须是 {max_results} 个
- 成语数量：必须是 {max_results} 个
- 严格按照JSON格式输出
- 不要添加任何解释性文字

**输出格式**：
请严格按照以下JSON格式返回：

{{
    "ai_explanation": "根据您的描述「{user_description}」，我为您推荐以下 {max_results} 个汉字、{max_results} 个词语和 {max_results} 个成语...",
    "characters": [
        {{
            "character": "智",
            "pinyin": "zhì",
            "meaning_en": "wisdom, intelligence",
            "meaning_zh": "智慧，聪明",
            "explanation": "为什么推荐这个汉字的详细解释",
            "cultural_context": "文化背景和典故",
            "score": 9.5
        }}
        // ... 总共 {max_results} 个汉字
    ],
    "words": [
        {{
            "word": "智慧",
            "pinyin": "zhì huì",
            "meaning_en": "wisdom, intelligence",
            "meaning_zh": "聪明才智",
            "explanation": "为什么推荐这个词语的详细解释",
            "cultural_context": "文化背景和典故",
            "score": 9.3
        }}
        // ... 总共 {max_results} 个词语
    ],
    "idioms": [
        {{
            "idiom": "自强不息",
            "pinyin": "zì qiáng bù xī",
            "meaning_en": "constantly strive to become stronger",
            "meaning_zh": "自己努力向上，永不懈怠",
            "explanation": "为什么推荐这个成语的详细解释",
            "origin_story": "典故来源",
            "character_count": 4,
            "score": 9.2
        }}
        // ... 总共 {max_results} 个成语
    ]
}}

**最终检查**：
- characters 数组必须包含 {max_results} 个汉字
- words 数组必须包含 {max_results} 个词语
- idioms 数组必须包含 {max_results} 个成语
- 每个推荐都要有完整的字段信息
- 确保推荐内容与用户描述高度相关
"""
        return prompt

    def _validate_recommendations(self, recommendations: Dict[str, Any], expected_count: int) -> bool:
        """验证AI返回的推荐数量是否正确"""
        try:
            characters = recommendations.get("characters", [])
            words = recommendations.get("words", [])
            idioms = recommendations.get("idioms", [])

            # 检查数量是否正确
            if len(characters) != expected_count:
                print(f"汉字数量不正确：期望{expected_count}个，实际{len(characters)}个")
                return False

            if len(words) != expected_count:
                print(f"词语数量不正确：期望{expected_count}个，实际{len(words)}个")
                return False

            if len(idioms) != expected_count:
                print(f"成语数量不正确：期望{expected_count}个，实际{len(idioms)}个")
                return False

            # 检查每个汉字是否有必要字段
            for i, char in enumerate(characters):
                required_fields = ["character", "pinyin", "meaning_en", "meaning_zh", "explanation"]
                for field in required_fields:
                    if not char.get(field):
                        print(f"汉字{i+1}缺少必要字段：{field}")
                        return False

            # 检查每个词语是否有必要字段
            for i, word in enumerate(words):
                required_fields = ["word", "pinyin", "meaning_en", "meaning_zh", "explanation"]
                for field in required_fields:
                    if not word.get(field):
                        print(f"词语{i+1}缺少必要字段：{field}")
                        return False

            # 检查每个成语是否有必要字段
            for i, idiom in enumerate(idioms):
                required_fields = ["idiom", "pinyin", "meaning_en", "meaning_zh", "explanation"]
                for field in required_fields:
                    if not idiom.get(field):
                        print(f"成语{i+1}缺少必要字段：{field}")
                        return False

            return True

        except Exception as e:
            print(f"验证推荐数据时出错：{e}")
            return False
