#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试显示版水印图片生成
"""

import asyncio
import sys
import os
from PIL import Image

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.services.image_generation_service import ImageGenerationService


async def test_display_watermark():
    """测试显示版水印图片生成"""
    
    print("🧪 测试显示版水印图片生成...")
    print("=" * 60)
    
    try:
        # 创建图像生成服务
        image_service = ImageGenerationService()
        print("✅ 图像生成服务实例化成功")
        
        # 测试参数
        prompt = "请为我生成文字'测试'的前臂纹身图，艺术字体，适合纹身，文化传统，精美作品，最佳质量，超高细节，清晰锐利"
        
        print(f"📝 测试参数:")
        print(f"   提示词: {prompt}")
        print(f"   高分辨率: False (会生成水印)")
        
        # 调用图像生成
        print(f"\n🎨 开始生成图片...")
        result = await image_service.generate_tattoo_image(
            prompt=prompt,
            high_resolution=False,  # 低分辨率会生成水印
            num_images=1
        )
        
        print("✅ 图像生成完成！")
        
        # 验证结果
        print(f"\n📊 生成结果验证:")
        print(f"   原图URL: {result.get('image_url', 'N/A')}")
        print(f"   缩略图URL: {result.get('thumbnail_url', 'N/A')}")
        print(f"   显示版水印图URL: {result.get('watermarked_url', 'N/A')}")
        print(f"   是否有水印: {result.get('is_watermarked', 'N/A')}")
        
        # 检查文件是否存在并获取尺寸
        upload_dir = image_service.upload_dir
        
        if result.get('image_url'):
            image_file = os.path.join(upload_dir, os.path.basename(result['image_url']))
            if os.path.exists(image_file):
                with Image.open(image_file) as img:
                    print(f"   原图尺寸: {img.size} (应该是1024x1024)")
            
        if result.get('thumbnail_url'):
            thumb_file = os.path.join(upload_dir, os.path.basename(result['thumbnail_url']))
            if os.path.exists(thumb_file):
                with Image.open(thumb_file) as img:
                    print(f"   缩略图尺寸: {img.size} (应该是256x256或更小)")
            
        if result.get('watermarked_url'):
            watermark_file = os.path.join(upload_dir, os.path.basename(result['watermarked_url']))
            if os.path.exists(watermark_file):
                with Image.open(watermark_file) as img:
                    print(f"   显示版水印图尺寸: {img.size} (应该是512x512)")
                    print(f"   显示版水印图文件大小: {os.path.getsize(watermark_file)} bytes")
        
        # 验证逻辑
        success = True
        
        if not result.get('image_url'):
            print("❌ 缺少原图URL")
            success = False
            
        if not result.get('thumbnail_url'):
            print("❌ 缺少缩略图URL")
            success = False
            
        if not result.get('watermarked_url'):
            print("❌ 缺少显示版水印图URL")
            success = False
            
        if not result.get('is_watermarked'):
            print("❌ 水印标识错误")
            success = False
        
        # 检查显示版水印图是否是512x512
        if result.get('watermarked_url'):
            watermark_file = os.path.join(upload_dir, os.path.basename(result['watermarked_url']))
            if os.path.exists(watermark_file):
                with Image.open(watermark_file) as img:
                    if img.size != (512, 512):
                        print(f"❌ 显示版水印图尺寸错误: {img.size}，应该是(512, 512)")
                        success = False
                    else:
                        print("✅ 显示版水印图尺寸正确: 512x512")
        
        if success:
            print("✅ 所有文件生成成功！")
            print("💡 现在前端应该显示512x512的带水印图片")
            print("💡 预览时显示1024x1024的无水印原图")
            return True
        else:
            print("❌ 部分文件生成失败")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        print("详细错误信息:")
        traceback.print_exc()
        return False


async def main():
    """主函数"""
    
    print("🧪 显示版水印图片生成测试")
    print("=" * 60)
    
    success = await test_display_watermark()
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 显示版水印图片测试通过！")
        print("💡 现在页面会显示512x512的带水印图片")
        print("💡 预览时显示1024x1024的无水印原图")
        print("💡 缩略图256x256用作备用")
    else:
        print("❌ 显示版水印图片测试失败")
    print("=" * 60)


if __name__ == "__main__":
    asyncio.run(main())
