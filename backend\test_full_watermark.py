#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试全图水印保护功能
"""

import asyncio
import sys
import os
from PIL import Image

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.services.image_generation_service import ImageGenerationService


async def test_full_watermark():
    """测试全图水印保护功能"""
    
    print("🧪 测试全图水印保护功能...")
    print("=" * 60)
    
    try:
        # 创建图像生成服务
        image_service = ImageGenerationService()
        print("✅ 图像生成服务实例化成功")
        
        # 测试参数
        prompt = "请为我生成文字'全图水印'的前臂纹身图，艺术字体，适合纹身，文化传统，精美作品，最佳质量，超高细节，清晰锐利"
        
        print(f"📝 测试参数:")
        print(f"   提示词: {prompt}")
        print(f"   高分辨率: False (会生成全图水印)")
        
        # 调用图像生成
        print(f"\n🎨 开始生成图片...")
        result = await image_service.generate_tattoo_image(
            prompt=prompt,
            high_resolution=False,  # 低分辨率会生成水印
            num_images=1
        )
        
        print("✅ 图像生成完成！")
        
        # 验证结果
        print(f"\n📊 生成结果验证:")
        print(f"   原图URL: {result.get('image_url', 'N/A')}")
        print(f"   缩略图URL: {result.get('thumbnail_url', 'N/A')}")
        print(f"   显示版水印图URL: {result.get('watermarked_url', 'N/A')}")
        print(f"   预览版水印图URL: {result.get('preview_url', 'N/A')}")
        print(f"   是否有水印: {result.get('is_watermarked', 'N/A')}")
        
        # 检查文件是否存在并获取尺寸
        upload_dir = image_service.upload_dir
        
        files_info = []
        
        if result.get('image_url'):
            image_file = os.path.join(upload_dir, os.path.basename(result['image_url']))
            if os.path.exists(image_file):
                with Image.open(image_file) as img:
                    files_info.append(("原图", img.size, "无水印", "购买后下载", image_file))
            
        if result.get('thumbnail_url'):
            thumb_file = os.path.join(upload_dir, os.path.basename(result['thumbnail_url']))
            if os.path.exists(thumb_file):
                with Image.open(thumb_file) as img:
                    files_info.append(("缩略图", img.size, "无水印", "备用显示", thumb_file))
            
        if result.get('watermarked_url'):
            watermark_file = os.path.join(upload_dir, os.path.basename(result['watermarked_url']))
            if os.path.exists(watermark_file):
                with Image.open(watermark_file) as img:
                    files_info.append(("显示版", img.size, "全图水印", "页面显示", watermark_file))
                    
        if result.get('preview_url'):
            preview_file = os.path.join(upload_dir, os.path.basename(result['preview_url']))
            if os.path.exists(preview_file):
                with Image.open(preview_file) as img:
                    files_info.append(("预览版", img.size, "全图水印", "预览弹窗", preview_file))
        
        print(f"\n📋 文件信息汇总:")
        print(f"   {'文件类型':<8} {'尺寸':<12} {'水印状态':<10} {'用途':<12} {'文件大小'}")
        print(f"   {'-'*8} {'-'*12} {'-'*10} {'-'*12} {'-'*10}")
        for file_type, size, watermark, usage, file_path in files_info:
            file_size = os.path.getsize(file_path) if os.path.exists(file_path) else 0
            file_size_kb = file_size // 1024
            print(f"   {file_type:<8} {str(size):<12} {watermark:<10} {usage:<12} {file_size_kb}KB")
        
        # 验证逻辑
        success = True
        expected_files = [
            ("原图", (1024, 1024)),
            ("缩略图", (256, 256)),
            ("显示版", (512, 512)),
            ("预览版", (768, 768)),
        ]
        
        for expected_type, expected_size in expected_files:
            found = False
            for file_type, size, _, _, _ in files_info:
                if file_type == expected_type and size == expected_size:
                    found = True
                    break
            
            if not found:
                print(f"❌ 缺少{expected_type}文件或尺寸不正确")
                success = False
            else:
                print(f"✅ {expected_type}文件正确: {expected_size}")
        
        # 检查水印版本是否存在
        watermark_files = [info for info in files_info if "水印" in info[2]]
        if len(watermark_files) >= 2:
            print(f"✅ 全图水印文件生成成功: {len(watermark_files)}个")
        else:
            print(f"❌ 全图水印文件不足: {len(watermark_files)}个")
            success = False
        
        if success:
            print(f"\n🎉 全图水印保护测试通过！")
            print(f"🔒 现在的强化保护策略:")
            print(f"   - 页面显示: 512x512全图水印网格")
            print(f"   - 预览弹窗: 768x768全图水印网格")
            print(f"   - 购买后: 1024x1024无水印原图")
            print(f"💡 水印覆盖整个图片，极难去除")
            return True
        else:
            print(f"❌ 部分文件生成失败")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        print("详细错误信息:")
        traceback.print_exc()
        return False


async def main():
    """主函数"""
    
    print("🧪 全图水印保护测试")
    print("=" * 60)
    
    success = await test_full_watermark()
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 全图水印保护测试通过！")
        print("🔒 现在用户无法轻易去除水印")
        print("💰 必须购买才能获得干净的高清版本")
        print("🛡️ 版权保护达到商业级别")
    else:
        print("❌ 全图水印保护测试失败")
    print("=" * 60)


if __name__ == "__main__":
    asyncio.run(main())
