<template>
  <div class="profile-page">
    <!-- 页面头部 -->
    <section class="profile-header">
      <div class="container">
        <div class="header-content">
          <div class="user-info">
            <el-avatar :size="80" :src="userStore.user?.avatar_url">
              {{ userStore.user?.username?.charAt(0).toUpperCase() }}
            </el-avatar>
            <div class="user-details">
              <h1>{{ userStore.user?.full_name || userStore.user?.username }}</h1>
              <p>{{ userStore.user?.email }}</p>
              <div class="user-badges">
                <el-tag v-if="userStore.user?.is_premium" type="warning" size="large">
                  <el-icon><Star /></el-icon>
                  高级会员
                </el-tag>
                <el-tag type="info" size="large">
                  <el-icon><Coin /></el-icon>
                  {{ userStore.user?.credits || 0 }} 积分
                </el-tag>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- 主要内容 -->
    <section class="profile-content">
      <div class="container">
        <el-tabs v-model="activeTab" class="profile-tabs">
          <!-- 个人信息 -->
          <el-tab-pane label="个人信息" name="info">
            <el-card class="info-card">
              <template #header>
                <h3>个人信息</h3>
              </template>
              
              <el-form
                ref="profileFormRef"
                :model="profileForm"
                :rules="profileRules"
                label-width="100px"
                size="large"
              >
                <el-form-item label="用户名" prop="username">
                  <el-input v-model="profileForm.username" disabled />
                </el-form-item>
                
                <el-form-item label="邮箱" prop="email">
                  <el-input v-model="profileForm.email" disabled />
                </el-form-item>
                
                <el-form-item label="全名" prop="full_name">
                  <el-input v-model="profileForm.full_name" placeholder="请输入您的全名" />
                </el-form-item>
                
                <el-form-item label="个人简介" prop="bio">
                  <el-input
                    v-model="profileForm.bio"
                    type="textarea"
                    :rows="3"
                    placeholder="介绍一下您自己..."
                    maxlength="200"
                    show-word-limit
                  />
                </el-form-item>
                
                <el-form-item label="语言偏好" prop="language_preference">
                  <el-select v-model="profileForm.language_preference" style="width: 200px">
                    <el-option label="中文" value="zh" />
                    <el-option label="English" value="en" />
                  </el-select>
                </el-form-item>
                
                <el-form-item>
                  <el-button type="primary" @click="updateProfile" :loading="isUpdating">
                    保存更改
                  </el-button>
                </el-form-item>
              </el-form>
            </el-card>
          </el-tab-pane>

          <!-- 纹身图生成历史 -->
          <el-tab-pane label="纹身图生成历史" name="designs">
            <div class="designs-section">
              <div class="section-header">
                <h3>纹身图生成历史</h3>
                <el-button type="primary" @click="$router.push('/tattoo-design')">
                  <el-icon><Plus /></el-icon>
                  新建设计
                </el-button>
              </div>
              
              <div v-if="userDesigns.length > 0" class="designs-grid">
                <div 
                  v-for="design in userDesigns" 
                  :key="design.id"
                  class="design-card"
                  @click="viewDesign(design)"
                >
                  <div class="design-image">
                    <img :src="`http://localhost:8000${design.watermarked_url || design.thumbnail_url || design.image_url}`" :alt="design.content" />
                    <div class="image-overlay">
                      <div class="image-actions">
                        <el-button size="small" @click.stop="previewImage(design)">
                          <el-icon><View /></el-icon>
                          预览
                        </el-button>
                        <el-button
                          type="primary"
                          size="small"
                          @click.stop="purchaseHighRes(design)"
                          v-if="design.is_watermarked"
                        >
                          <el-icon><Download /></el-icon>
                          购买高清
                        </el-button>
                        <el-button
                          type="success"
                          size="small"
                          @click.stop="downloadImage(design)"
                          v-else
                        >
                          <el-icon><Download /></el-icon>
                          下载
                        </el-button>
                      </div>
                    </div>
                  </div>
                  <div class="design-info">
                    <div class="design-content">{{ design.content }}</div>
                    <div class="design-meta">
                      <span>{{ design.style }}</span>
                      <span>{{ formatDate(design.created_at) }}</span>
                    </div>
                  </div>
                </div>
              </div>
              
              <div v-else class="empty-designs">
                <el-icon size="64"><Picture /></el-icon>
                <h3>还没有生成纹身图</h3>
                <p>开始生成您的第一个纹身设计吧！</p>
                <el-button type="primary" @click="$router.push('/recommend')">
                  开始生成
                </el-button>
              </div>
            </div>
          </el-tab-pane>

          <!-- 积分管理 -->
          <el-tab-pane label="积分管理" name="credits">
            <el-card class="credits-card">
              <template #header>
                <h3>积分管理</h3>
              </template>
              
              <div class="credits-overview">
                <div class="credits-balance">
                  <div class="balance-icon">
                    <el-icon size="48"><Coin /></el-icon>
                  </div>
                  <div class="balance-info">
                    <div class="balance-amount">{{ userStore.user?.credits || 0 }}</div>
                    <div class="balance-label">可用积分</div>
                  </div>
                </div>
                
                <div class="credits-actions">
                  <el-button type="primary" size="large" @click="showRechargeDialog = true">
                    <el-icon><Plus /></el-icon>
                    充值积分
                  </el-button>
                  
                  <el-button v-if="!userStore.user?.is_premium" type="warning" size="large">
                    <el-icon><Star /></el-icon>
                    升级高级会员
                  </el-button>
                </div>
              </div>
              
              <div class="credits-usage">
                <h4>积分使用说明</h4>
                <div class="usage-list">
                  <div class="usage-item">
                    <el-icon><MagicStick /></el-icon>
                    <span>AI推荐：1积分/次</span>
                  </div>
                  <div class="usage-item">
                    <el-icon><Picture /></el-icon>
                    <span>标准纹身设计：2积分/次</span>
                  </div>
                  <div class="usage-item">
                    <el-icon><Picture /></el-icon>
                    <span>高清纹身设计：5积分/次</span>
                  </div>
                  <div class="usage-item">
                    <el-icon><Unlock /></el-icon>
                    <span>解锁高清图片：10积分/次</span>
                  </div>
                </div>
              </div>
            </el-card>
          </el-tab-pane>

          <!-- 账户设置 -->
          <el-tab-pane label="账户设置" name="settings">
            <el-card class="settings-card">
              <template #header>
                <h3>账户设置</h3>
              </template>
              
              <div class="settings-section">
                <h4>密码修改</h4>
                <el-form :model="passwordForm" label-width="120px">
                  <el-form-item label="当前密码">
                    <el-input v-model="passwordForm.current_password" type="password" />
                  </el-form-item>
                  <el-form-item label="新密码">
                    <el-input v-model="passwordForm.new_password" type="password" />
                  </el-form-item>
                  <el-form-item label="确认新密码">
                    <el-input v-model="passwordForm.confirm_password" type="password" />
                  </el-form-item>
                  <el-form-item>
                    <el-button type="primary">更新密码</el-button>
                  </el-form-item>
                </el-form>
              </div>
              
              <el-divider />
              
              <div class="danger-zone">
                <h4>危险操作</h4>
                <el-button type="danger" @click="showDeleteDialog = true">
                  删除账户
                </el-button>
              </div>
            </el-card>
          </el-tab-pane>
        </el-tabs>
      </div>
    </section>

    <!-- 充值对话框 -->
    <el-dialog v-model="showRechargeDialog" title="充值积分" width="400px">
      <div class="recharge-content">
        <p>选择充值套餐：</p>
        <div class="recharge-options">
          <div class="recharge-option">
            <div class="option-credits">100积分</div>
            <div class="option-price">¥10</div>
            <el-button type="primary">充值</el-button>
          </div>
          <div class="recharge-option">
            <div class="option-credits">500积分</div>
            <div class="option-price">¥45</div>
            <el-button type="primary">充值</el-button>
          </div>
          <div class="recharge-option">
            <div class="option-credits">1000积分</div>
            <div class="option-price">¥80</div>
            <el-button type="primary">充值</el-button>
          </div>
        </div>
      </div>
    </el-dialog>

    <!-- 删除账户确认对话框 -->
    <el-dialog v-model="showDeleteDialog" title="删除账户" width="400px">
      <div class="delete-content">
        <el-icon size="48" color="#f56c6c"><WarningFilled /></el-icon>
        <p>此操作将永久删除您的账户和所有数据，无法恢复。</p>
        <p>请输入您的密码确认：</p>
        <el-input v-model="deletePassword" type="password" placeholder="请输入密码" />
      </div>
      <template #footer>
        <el-button @click="showDeleteDialog = false">取消</el-button>
        <el-button type="danger" :disabled="!deletePassword">确认删除</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage, type FormInstance, type FormRules } from 'element-plus'
import {
  Star, Coin, Plus, Picture, MagicStick, Unlock, WarningFilled, View, Download
} from '@element-plus/icons-vue'
import { useUserStore } from '@/stores/counter'
import { TattooService } from '@/services/tattoo'
import type { TattooImage } from '@/types'

const route = useRoute()
const router = useRouter()
const userStore = useUserStore()

const activeTab = ref('info')
const isUpdating = ref(false)
const showRechargeDialog = ref(false)
const showDeleteDialog = ref(false)
const deletePassword = ref('')

const profileFormRef = ref<FormInstance>()
const userDesigns = ref<TattooImage[]>([])

// 个人信息表单
const profileForm = reactive({
  username: '',
  email: '',
  full_name: '',
  bio: '',
  language_preference: 'zh'
})

// 密码修改表单
const passwordForm = reactive({
  current_password: '',
  new_password: '',
  confirm_password: ''
})

// 表单验证规则
const profileRules: FormRules = {
  full_name: [
    { max: 50, message: '全名不能超过50个字符', trigger: 'blur' }
  ],
  bio: [
    { max: 200, message: '个人简介不能超过200个字符', trigger: 'blur' }
  ]
}

onMounted(async () => {
  // 从URL参数获取激活的标签
  const tab = route.query.tab as string
  if (tab) {
    activeTab.value = tab
  }

  // 初始化用户信息
  if (userStore.user) {
    profileForm.username = userStore.user.username
    profileForm.email = userStore.user.email
    profileForm.full_name = userStore.user.full_name || ''
    profileForm.bio = userStore.user.bio || ''
    profileForm.language_preference = userStore.user.language_preference || 'zh'
  }

  // 加载用户设计
  await loadUserDesigns()
})

// 加载用户设计
const loadUserDesigns = async () => {
  try {
    const designs = await TattooService.getUserTattooHistory()
    userDesigns.value = designs
  } catch (error) {
    console.error('加载用户设计失败:', error)
  }
}

// 更新个人资料
const updateProfile = async () => {
  if (!profileFormRef.value) return

  try {
    await profileFormRef.value.validate()
    
    isUpdating.value = true
    
    // 这里应该调用API更新用户信息
    // await UserService.updateProfile(profileForm)
    
    ElMessage.success('个人资料更新成功')
    
  } catch (error) {
    console.error('更新个人资料失败:', error)
    ElMessage.error('更新失败，请稍后重试')
  } finally {
    isUpdating.value = false
  }
}

// 查看设计
const viewDesign = (design: TattooImage) => {
  router.push({
    path: '/tattoo-design',
    query: {
      content: design.content,
      style: design.style,
      position: design.position
    }
  })
}

// 格式化日期
const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleDateString('zh-CN')
}

// 预览图片
const previewImage = (image: TattooImage) => {
  window.open(`http://localhost:8000${image.image_url}`, '_blank')
}

// 购买高清版
const purchaseHighRes = async (image: TattooImage) => {
  try {
    const result = await TattooService.unlockHighResolution(image.id)

    // 更新图片信息
    const index = userDesigns.value.findIndex(img => img.id === image.id)
    if (index !== -1) {
      userDesigns.value[index] = { ...result, is_watermarked: false }
    }

    ElMessage.success('高清版本解锁成功！')

  } catch (error) {
    console.error('购买高清版失败:', error)
    ElMessage.error('购买失败，请稍后重试')
  }
}

// 下载图片
const downloadImage = (image: TattooImage) => {
  const link = document.createElement('a')
  link.href = `http://localhost:8000${image.image_url}`
  link.download = `tattoo-${image.content}-${Date.now()}.png`
  link.click()
}
</script>

<style scoped>
.profile-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
}

/* 页面头部 */
.profile-header {
  padding: 60px 0 40px;
  background: linear-gradient(135deg, rgba(212, 175, 55, 0.1) 0%, rgba(255, 215, 0, 0.05) 100%);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 24px;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 24px;
}

.user-details h1 {
  font-size: 2rem;
  font-weight: 600;
  color: #ffffff;
  margin: 0 0 8px 0;
}

.user-details p {
  color: rgba(255, 255, 255, 0.7);
  margin: 0 0 16px 0;
}

.user-badges {
  display: flex;
  gap: 12px;
}

/* 主要内容 */
.profile-content {
  padding: 40px 0;
}

:deep(.profile-tabs .el-tabs__header) {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 12px;
  padding: 8px;
  margin-bottom: 30px;
}

:deep(.profile-tabs .el-tabs__nav-wrap::after) {
  display: none;
}

:deep(.profile-tabs .el-tabs__item) {
  color: rgba(255, 255, 255, 0.7);
  border-radius: 8px;
  margin-right: 8px;
}

:deep(.profile-tabs .el-tabs__item.is-active) {
  color: #ffd700;
  background: rgba(255, 215, 0, 0.1);
}

/* 卡片样式 */
.info-card,
.credits-card,
.settings-card {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
}

.info-card h3,
.credits-card h3,
.settings-card h3 {
  color: #ffffff;
  margin: 0;
}

/* 设计网格 */
.designs-section {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  padding: 24px;
  backdrop-filter: blur(10px);
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.section-header h3 {
  color: #ffffff;
  margin: 0;
}

.designs-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 20px;
}

.design-card {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  overflow: hidden;
  cursor: pointer;
  transition: all 0.3s ease;
}

.design-card:hover {
  transform: translateY(-5px);
  border-color: rgba(255, 215, 0, 0.3);
}

.design-image {
  position: relative;
  width: 100%;
  height: 150px;
  overflow: hidden;
}

.design-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.image-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(to bottom, transparent 0%, rgba(0, 0, 0, 0.8) 100%);
  display: flex;
  align-items: flex-end;
  justify-content: center;
  padding: 12px;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.design-card:hover .image-overlay {
  opacity: 1;
}

.image-actions {
  display: flex;
  gap: 8px;
  justify-content: center;
}

.design-info {
  padding: 12px;
}

.design-content {
  font-size: 1.1rem;
  font-weight: 600;
  color: #ffd700;
  font-family: 'SimSun', serif;
  margin-bottom: 4px;
}

.design-meta {
  display: flex;
  justify-content: space-between;
  font-size: 0.85rem;
  color: rgba(255, 255, 255, 0.7);
}

/* 积分管理 */
.credits-overview {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
  padding: 24px;
  background: rgba(255, 215, 0, 0.1);
  border-radius: 12px;
  border: 1px solid rgba(255, 215, 0, 0.2);
}

.credits-balance {
  display: flex;
  align-items: center;
  gap: 16px;
}

.balance-icon {
  color: #ffd700;
}

.balance-amount {
  font-size: 2rem;
  font-weight: 600;
  color: #ffd700;
}

.balance-label {
  color: rgba(255, 255, 255, 0.8);
}

.credits-actions {
  display: flex;
  gap: 12px;
}

.credits-usage h4 {
  color: #ffffff;
  margin: 0 0 16px 0;
}

.usage-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.usage-item {
  display: flex;
  align-items: center;
  gap: 8px;
  color: rgba(255, 255, 255, 0.8);
}

.usage-item .el-icon {
  color: #ffd700;
}

/* 充值对话框 */
.recharge-options {
  display: flex;
  flex-direction: column;
  gap: 16px;
  margin-top: 16px;
}

.recharge-option {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 8px;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.option-credits {
  font-weight: 600;
  color: #ffd700;
}

.option-price {
  color: #666;
}

/* 删除账户对话框 */
.delete-content {
  text-align: center;
  padding: 20px;
}

.delete-content p {
  margin: 16px 0;
  color: #666;
}

/* 空状态 */
.empty-designs {
  text-align: center;
  padding: 60px 20px;
  color: rgba(255, 255, 255, 0.6);
}

.empty-designs h3 {
  color: #ffffff;
  margin: 16px 0 8px 0;
}

/* 表单样式覆盖 */
:deep(.el-form-item__label) {
  color: rgba(255, 255, 255, 0.8);
}

:deep(.el-card__header) {
  background: rgba(255, 255, 255, 0.02);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

:deep(.el-card__body) {
  background: transparent;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .user-info {
    flex-direction: column;
    text-align: center;
  }
  
  .credits-overview {
    flex-direction: column;
    gap: 20px;
  }
  
  .designs-grid {
    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
  }
}
</style>
