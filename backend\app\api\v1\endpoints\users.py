"""
用户相关API端点
User Related API Endpoints
"""

from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from typing import List
from app.schemas.tattoo import TattooImageResponse

from app.db.database import get_db
from app.models.user import User
from app.schemas.user import UserResponse, UserUpdate
from app.services.auth_service import AuthService

router = APIRouter()


@router.get("/me", response_model=UserResponse, summary="获取当前用户信息")
async def get_current_user_profile(current_user: User = Depends(AuthService.get_current_active_user)):
    """获取当前登录用户的详细信息"""
    return current_user


@router.put("/me", response_model=UserResponse, summary="更新用户资料")
async def update_user_profile(
    user_update: UserUpdate,
    current_user: User = Depends(AuthService.get_current_active_user),
    db: Session = Depends(get_db),
):
    """更新当前用户的资料信息"""

    # 更新用户信息
    if user_update.full_name is not None:
        current_user.full_name = user_update.full_name
    if user_update.bio is not None:
        current_user.bio = user_update.bio
    if user_update.language_preference is not None:
        current_user.language_preference = user_update.language_preference
    if user_update.avatar_url is not None:
        current_user.avatar_url = user_update.avatar_url

    db.commit()
    db.refresh(current_user)

    return current_user


@router.get("/credits", summary="获取用户积分")
async def get_user_credits(current_user: User = Depends(AuthService.get_current_active_user)):
    """获取当前用户的积分余额"""
    return {"credits": current_user.credits, "is_premium": current_user.is_premium}


@router.post("/credits/add", summary="增加用户积分")
async def add_user_credits(
    amount: int, current_user: User = Depends(AuthService.get_current_active_user), db: Session = Depends(get_db)
):
    """增加用户积分（通常在支付成功后调用）"""
    if amount <= 0:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="积分数量必须大于0")

    current_user.credits += amount
    db.commit()
    db.refresh(current_user)

    return {"message": f"成功增加 {amount} 积分", "new_balance": current_user.credits}


@router.post("/credits/deduct", summary="扣除用户积分")
async def deduct_user_credits(
    amount: int, current_user: User = Depends(AuthService.get_current_active_user), db: Session = Depends(get_db)
):
    """扣除用户积分（通常在使用服务时调用）"""
    if amount <= 0:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="积分数量必须大于0")

    if current_user.credits < amount:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="积分余额不足")

    current_user.credits -= amount
    db.commit()
    db.refresh(current_user)

    return {"message": f"成功扣除 {amount} 积分", "new_balance": current_user.credits}


@router.get("/tattoo-history", response_model=List[TattooImageResponse], summary="获取用户纹身历史")
async def get_user_tattoo_history(
    limit: int = 10, current_user: User = Depends(AuthService.get_current_active_user), db: Session = Depends(get_db)
):
    """获取用户的纹身设计历史"""
    from app.models.tattoo import TattooImage, TattooRequest

    # 查询用户的纹身图片历史，按创建时间倒序
    tattoo_images = (
        db.query(TattooImage)
        .join(TattooRequest)
        .filter(TattooRequest.user_id == current_user.id)
        .order_by(TattooImage.created_at.desc())
        .limit(limit)
        .all()
    )

    return tattoo_images


@router.get("/tattoo-stats", summary="获取用户纹身统计")
async def get_user_tattoo_stats(
    current_user: User = Depends(AuthService.get_current_active_user), db: Session = Depends(get_db)
):
    """获取用户的纹身统计信息"""
    from app.models.tattoo import TattooImage, TattooRequest

    # 查询用户的纹身图片总数
    total_count = db.query(TattooImage).join(TattooRequest).filter(TattooRequest.user_id == current_user.id).count()

    # 查询不同设计数（按content分组）
    unique_designs = (
        db.query(TattooImage.content)
        .join(TattooRequest)
        .filter(TattooRequest.user_id == current_user.id)
        .distinct()
        .count()
    )

    return {"total_images": total_count, "unique_designs": unique_designs, "user_id": current_user.id}
