<template>
  <header class="app-header">
    <div class="header-container">
      <!-- Logo和品牌名 -->
      <div class="brand" @click="$router.push('/')">
        <div class="logo">
          <span class="chinese-char">墨</span>
        </div>
        <div class="brand-text">
          <h1>墨痕智纹</h1>
          <p>Chinese Character Tattoo AI</p>
        </div>
      </div>

      <!-- 导航菜单 -->
      <nav class="nav-menu">
        <div class="nav-buttons">
          <el-button
            text
            :class="{ active: route.path === '/' }"
            @click="$router.push('/')"
          >
            <el-icon><House /></el-icon>
            <span class="nav-text">首页</span>
          </el-button>

          <el-button
            text
            :class="{ active: route.path === '/recommend' }"
            @click="$router.push('/recommend')"
            v-if="userStore.isAuthenticated"
          >
            <el-icon><MagicStick /></el-icon>
            <span class="nav-text">AI推荐</span>
          </el-button>

          <el-button
            text
            :class="{ active: route.path === '/about' }"
            @click="$router.push('/about')"
          >
            <el-icon><Picture /></el-icon>
            <span class="nav-text">关于</span>
          </el-button>
        </div>
      </nav>

      <!-- 用户操作区域 -->
      <div class="user-actions">
        <!-- 未登录状态 -->
        <template v-if="!userStore.isAuthenticated">
          <el-button type="text" @click="$router.push('/login')">
            登录
          </el-button>
          <el-button type="primary" @click="$router.push('/register')">
            注册
          </el-button>
        </template>

        <!-- 已登录状态 -->
        <template v-else>
          <!-- 用户菜单 -->
          <el-dropdown @command="handleUserCommand">
            <div class="user-avatar">
              <el-avatar 
                :src="userStore.user?.avatar_url" 
                :size="32"
              >
                {{ userStore.user?.username?.charAt(0).toUpperCase() }}
              </el-avatar>
              <span class="username">{{ userStore.user?.username }}</span>
              <el-icon><ArrowDown /></el-icon>
            </div>
            
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item command="dashboard">
                  <el-icon><User /></el-icon>
                  仪表板
                </el-dropdown-item>
                <el-dropdown-item command="profile">
                  <el-icon><Setting /></el-icon>
                  个人资料
                </el-dropdown-item>
                <el-dropdown-item divided command="logout">
                  <el-icon><SwitchButton /></el-icon>
                  退出登录
                </el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </template>
      </div>
    </div>
  </header>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useUserStore } from '@/stores/counter'
import {
  House,
  Search,
  MagicStick,
  Picture,
  Coin,
  User,
  Setting,
  SwitchButton,
  ArrowDown
} from '@element-plus/icons-vue'

const route = useRoute()
const router = useRouter()
const userStore = useUserStore()



// 处理用户下拉菜单命令
const handleUserCommand = (command: string) => {
  switch (command) {
    case 'dashboard':
      router.push('/dashboard')
      break
    case 'profile':
      router.push('/profile')
      break
    case 'logout':
      userStore.logout()
      router.push('/')
      break
  }
}
</script>

<style scoped>
.app-header {
  background: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(20px);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  position: sticky;
  top: 0;
  z-index: 1000;
}

.header-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 24px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 64px;
}

/* 品牌区域 */
.brand {
  display: flex;
  align-items: center;
  cursor: pointer;
  transition: all 0.3s ease;
}

.brand:hover {
  transform: translateY(-1px);
}

.logo {
  width: 48px;
  height: 48px;
  background: linear-gradient(135deg, #d4af37 0%, #ffd700 100%);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 12px;
  box-shadow: 0 4px 12px rgba(212, 175, 55, 0.3);
}

.chinese-char {
  font-size: 24px;
  font-weight: bold;
  color: #1a1a1a;
  font-family: 'SimSun', serif;
}

.brand-text h1 {
  font-size: 20px;
  font-weight: 600;
  margin: 0;
  color: #ffffff;
  font-family: 'SimSun', serif;
}

.brand-text p {
  font-size: 12px;
  margin: 0;
  color: rgba(255, 255, 255, 0.7);
  font-style: italic;
}

/* 导航菜单 */
.nav-menu {
  flex: 1;
  display: flex;
  justify-content: center;
  margin: 0 40px;
}

.nav-buttons {
  display: flex;
  gap: 8px;
  align-items: center;
}

.nav-buttons .el-button {
  color: rgba(255, 255, 255, 0.8);
  font-size: 14px;
  padding: 8px 16px;
  border-radius: 8px;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 6px;
}

.nav-buttons .el-button:hover {
  color: #ffd700;
  background: rgba(255, 215, 0, 0.1);
}

.nav-buttons .el-button.active {
  color: #ffd700;
  background: rgba(255, 215, 0, 0.15);
}

.nav-buttons .el-button .el-icon {
  font-size: 16px;
}

/* 用户操作区域 */
.user-actions {
  display: flex;
  align-items: center;
  gap: 16px;
}

.credits-display {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 6px 12px;
  background: rgba(255, 215, 0, 0.1);
  border: 1px solid rgba(255, 215, 0, 0.3);
  border-radius: 20px;
  color: #ffd700;
  font-weight: 600;
}

.user-avatar {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  padding: 4px 8px;
  border-radius: 20px;
  transition: all 0.3s ease;
}

.user-avatar:hover {
  background: rgba(255, 255, 255, 0.1);
}

.username {
  color: #ffffff;
  font-weight: 500;
  max-width: 100px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .header-container {
    padding: 0 16px;
    gap: 12px;
  }

  .brand {
    cursor: pointer;
  }

  .logo {
    width: 40px;
    height: 40px;
  }

  .chinese-char {
    font-size: 20px;
  }

  .brand-text h1 {
    font-size: 16px;
  }

  .brand-text p {
    display: none;
  }

  .nav-menu {
    max-width: 200px;
  }

  .nav-buttons {
    gap: 4px;
  }

  .nav-buttons .el-button {
    padding: 8px;
    min-width: 40px;
    font-size: 0.8rem;
  }

  .nav-buttons .el-button .el-icon {
    font-size: 18px;
  }

  /* 移动端隐藏导航文字，只显示图标 */
  .nav-text {
    display: none;
  }

  .user-avatar {
    gap: 8px;
  }

  .user-avatar .el-avatar {
    width: 28px;
    height: 28px;
  }

  .username {
    display: none;
  }

  /* 移动端显示简化的用户菜单 */
  .user-actions {
    gap: 8px;
  }

  .user-actions .el-button {
    padding: 8px 12px;
    font-size: 0.9rem;
  }
}
</style>
