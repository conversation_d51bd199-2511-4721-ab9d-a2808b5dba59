"""
纹身相关数据模型
Tattoo Related Data Model
"""

from sqlalchemy import Column, Integer, String, Text, DateTime, Boolean, JSON, ForeignKey, Float
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from app.db.database import Base


class TattooRequest(Base):
    """纹身请求模型"""

    __tablename__ = "tattoo_requests"

    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)

    # 用户输入
    keywords = Column(JSON, nullable=False)  # 关键词数组
    description = Column(Text, nullable=True)  # 情感描述
    personal_meaning = Column(Text, nullable=True)  # 个人意义描述

    # AI推荐结果
    recommended_characters = Column(JSON, nullable=True)  # 推荐的汉字
    recommended_idioms = Column(JSON, nullable=True)  # 推荐的成语
    ai_explanation = Column(Text, nullable=True)  # AI解释

    # 用户选择
    selected_type = Column(String(20), nullable=True)  # character 或 idiom
    selected_content = Column(String(50), nullable=True)  # 选择的汉字或成语

    # 纹身设计偏好
    preferred_style = Column(String(50), nullable=True)  # 书法风格
    preferred_position = Column(String(50), nullable=True)  # 身体部位
    size_preference = Column(String(20), nullable=True)  # 大小偏好

    # 状态
    status = Column(String(20), default="pending")  # pending, completed, cancelled
    is_paid = Column(Boolean, default=False)

    # 时间戳
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), nullable=True)

    # 关系
    user = relationship("User", back_populates="tattoo_requests")
    generated_images = relationship("TattooImage", back_populates="request")

    def __repr__(self):
        return f"<TattooRequest(id={self.id}, user_id={self.user_id}, status='{self.status}')>"


class TattooImage(Base):
    """纹身图片模型"""

    __tablename__ = "tattoo_images"

    id = Column(Integer, primary_key=True, index=True)
    request_id = Column(Integer, ForeignKey("tattoo_requests.id"), nullable=False)

    # 图片信息
    image_url = Column(String(500), nullable=False)
    thumbnail_url = Column(String(500), nullable=True)
    watermarked_url = Column(String(500), nullable=True)  # 带水印显示版本(512x512)
    preview_url = Column(String(500), nullable=True)  # 带水印预览版本(768x768)

    # 生成参数
    style = Column(String(50), nullable=False)  # 生成风格
    position = Column(String(50), nullable=False)  # 身体部位
    content = Column(String(100), nullable=False)  # 汉字或成语内容

    # 图片属性
    width = Column(Integer, nullable=True)
    height = Column(Integer, nullable=True)
    file_size = Column(Integer, nullable=True)  # 文件大小（字节）

    # AI生成信息
    generation_prompt = Column(Text, nullable=True)  # 生成提示词
    generation_model = Column(String(50), nullable=True)  # 使用的AI模型
    generation_time = Column(Float, nullable=True)  # 生成耗时（秒）

    # 状态和质量
    is_high_resolution = Column(Boolean, default=False)
    is_watermarked = Column(Boolean, default=True)
    quality_score = Column(Float, nullable=True)  # 质量评分

    # 时间戳
    created_at = Column(DateTime(timezone=True), server_default=func.now())

    # 关系
    request = relationship("TattooRequest", back_populates="generated_images")

    def __repr__(self):
        return f"<TattooImage(id={self.id}, request_id={self.request_id}, style='{self.style}')>"


class TattooStyle(Base):
    """纹身风格模型"""

    __tablename__ = "tattoo_styles"

    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(100), unique=True, nullable=False)
    name_zh = Column(String(100), nullable=False)  # 中文名称
    description = Column(Text, nullable=True)

    # 风格特征
    characteristics = Column(JSON, nullable=True)  # 特征描述
    suitable_positions = Column(JSON, nullable=True)  # 适合的身体部位
    difficulty_level = Column(Integer, default=1)  # 难度等级

    # 示例和参考
    example_images = Column(JSON, nullable=True)  # 示例图片URL数组
    prompt_template = Column(Text, nullable=True)  # AI生成提示词模板

    # 状态
    is_active = Column(Boolean, default=True)
    is_premium = Column(Boolean, default=False)  # 是否为付费风格

    # 时间戳
    created_at = Column(DateTime(timezone=True), server_default=func.now())

    def __repr__(self):
        return f"<TattooStyle(id={self.id}, name='{self.name}')>"
