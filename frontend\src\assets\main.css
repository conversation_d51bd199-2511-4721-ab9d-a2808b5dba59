@import './base.css';

/* 移除限制性的全局样式，让各页面自己控制布局 */

a,
.green {
  text-decoration: none;
  color: hsla(160, 100%, 37%, 1);
  transition: 0.4s;
  padding: 3px;
}

@media (hover: hover) {
  a:hover {
    background-color: hsla(160, 100%, 37%, 0.2);
  }
}

/* 移除强制的大屏幕布局，让各页面自己控制 */

/* 全局移动端优化 */
@media (max-width: 768px) {

  /* 确保内容不会超出屏幕 */
  * {
    box-sizing: border-box;
  }

  /* 优化触摸目标大小 */
  .el-button {
    min-height: 44px;
    min-width: 44px;
  }

  /* 优化表单输入 */
  .el-input__inner,
  .el-textarea__inner {
    font-size: 16px;
    /* 防止iOS缩放 */
  }

  /* 优化对话框 */
  .el-dialog {
    width: 95% !important;
    margin: 0 auto;
  }

  .el-dialog__body {
    padding: 16px;
  }

  /* 优化标签页 */
  .el-tabs__nav-scroll {
    overflow-x: auto;
  }

  .el-tabs__item {
    padding: 0 12px;
    font-size: 14px;
  }

  /* 优化卡片 */
  .el-card {
    margin: 8px;
  }

  .el-card__body {
    padding: 16px;
  }
}