#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试字体类型功能
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.services.tattoo_service import TattooService
from app.db.database import get_db


def test_prompt_generation():
    """测试提示词生成功能"""
    
    print("🧪 测试字体类型提示词生成...")
    print("=" * 60)
    
    try:
        # 获取数据库会话
        db = next(get_db())
        
        # 创建纹身服务
        tattoo_service = TattooService(db)
        
        # 测试参数
        content = "龙"
        style = "calligraphy"
        position = "forearm"
        high_resolution = False
        
        print(f"📝 测试参数:")
        print(f"   内容: {content}")
        print(f"   风格: {style}")
        print(f"   位置: {position}")
        print(f"   高分辨率: {high_resolution}")
        
        # 测试简体字体
        print(f"\n🔤 简体字体提示词:")
        simplified_prompt = tattoo_service._build_generation_prompt(
            content, style, position, high_resolution, "simplified"
        )
        print(f"   {simplified_prompt}")
        
        # 测试繁体字体
        print(f"\n🔤 繁体字体提示词:")
        traditional_prompt = tattoo_service._build_generation_prompt(
            content, style, position, high_resolution, "traditional"
        )
        print(f"   {traditional_prompt}")
        
        # 验证差异
        print(f"\n📊 提示词对比:")
        print(f"   简体包含'简体': {'简体' in simplified_prompt}")
        print(f"   繁体包含'繁体': {'繁体' in traditional_prompt}")
        
        if "简体" in simplified_prompt and "繁体" in traditional_prompt:
            print("✅ 字体类型正确添加到提示词中")
            return True
        else:
            print("❌ 字体类型未正确添加到提示词中")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        print("详细错误信息:")
        traceback.print_exc()
        return False
    finally:
        db.close()


def test_different_contents():
    """测试不同内容的字体类型处理"""
    
    print("\n🔍 测试不同内容的字体类型处理...")
    print("=" * 60)
    
    try:
        db = next(get_db())
        tattoo_service = TattooService(db)
        
        test_cases = [
            ("龙", "单字"),
            ("龙腾虎跃", "成语"),
            ("无为", "词语"),
            ("百折不挠", "四字成语")
        ]
        
        for content, content_type in test_cases:
            print(f"\n📝 测试内容: {content} ({content_type})")
            
            # 简体提示词
            simplified = tattoo_service._build_generation_prompt(
                content, "calligraphy", "forearm", False, "simplified"
            )
            
            # 繁体提示词
            traditional = tattoo_service._build_generation_prompt(
                content, "calligraphy", "forearm", False, "traditional"
            )
            
            print(f"   简体: ...{simplified[-50:]}")
            print(f"   繁体: ...{traditional[-50:]}")
            
            # 验证字体类型标识
            has_simplified = "简体" in simplified
            has_traditional = "繁体" in traditional
            
            print(f"   ✅ 简体标识: {has_simplified}")
            print(f"   ✅ 繁体标识: {has_traditional}")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False
    finally:
        db.close()


def test_api_schema():
    """测试API模式定义"""
    
    print("\n🔧 测试API模式定义...")
    print("=" * 60)
    
    try:
        from app.schemas.tattoo import TattooGenerationRequest
        
        # 测试创建请求对象
        request_data = {
            "request_id": 1,
            "style": "calligraphy",
            "position": "forearm",
            "content": "龙",
            "high_resolution": False,
            "font_type": "traditional"
        }
        
        request = TattooGenerationRequest(**request_data)
        
        print(f"✅ API模式创建成功:")
        print(f"   request_id: {request.request_id}")
        print(f"   content: {request.content}")
        print(f"   font_type: {request.font_type}")
        
        # 测试默认值
        default_request = TattooGenerationRequest(
            request_id=2,
            style="calligraphy",
            position="forearm",
            content="龙"
        )
        
        print(f"\n✅ 默认字体类型: {default_request.font_type}")
        
        return True
        
    except Exception as e:
        print(f"❌ API模式测试失败: {e}")
        return False


def main():
    """主函数"""
    
    print("🧪 字体类型功能测试")
    print("=" * 60)
    
    # 测试1: 提示词生成
    test1_success = test_prompt_generation()
    
    # 测试2: 不同内容处理
    test2_success = test_different_contents()
    
    # 测试3: API模式定义
    test3_success = test_api_schema()
    
    print("\n" + "=" * 60)
    print("📋 测试总结:")
    print(f"   提示词生成测试: {'✅ 通过' if test1_success else '❌ 失败'}")
    print(f"   不同内容处理测试: {'✅ 通过' if test2_success else '❌ 失败'}")
    print(f"   API模式定义测试: {'✅ 通过' if test3_success else '❌ 失败'}")
    
    if test1_success and test2_success and test3_success:
        print("🎉 所有测试通过！字体类型功能正常")
        print("💡 现在用户可以选择简体或繁体字体生成纹身图")
    else:
        print("⚠️ 部分测试失败，需要检查实现")
    print("=" * 60)


if __name__ == "__main__":
    main()
