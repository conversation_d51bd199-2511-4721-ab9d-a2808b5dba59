#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化的字体类型功能测试
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))


def test_prompt_logic():
    """测试提示词生成逻辑（不依赖数据库）"""

    print("🧪 测试字体类型提示词生成逻辑...")
    print("=" * 60)

    def build_generation_prompt(
        content: str, style: str, position: str, high_resolution: bool, font_type: str = "simplified"
    ) -> str:
        """简化版提示词构建函数"""

        # 身体部位中文映射
        position_map = {
            "forearm": "前臂",
            "shoulder": "肩膀",
            "back": "后背",
            "chest": "胸部",
            "wrist": "手腕",
            "ankle": "脚踝",
        }

        chinese_position = position_map.get(position, position)

        # 字体类型映射
        font_type_text = "繁体" if font_type == "traditional" else "简体"

        # 中文默认提示词模板，包含字体类型
        prompt = f"请为我生成文字'{content}'的{chinese_position}纹身图，{font_type_text}中文，中国传统书法风格，黑色墨水，艺术字体，适合纹身，文化传统，精美作品，最佳质量，超高细节，清晰锐利"

        if high_resolution:
            prompt += "，超高分辨率，4K画质，专业艺术作品"

        return prompt.strip()

    try:
        # 测试参数
        content = "龙"
        style = "calligraphy"
        position = "forearm"
        high_resolution = False

        print(f"📝 测试参数:")
        print(f"   内容: {content}")
        print(f"   风格: {style}")
        print(f"   位置: {position}")
        print(f"   高分辨率: {high_resolution}")

        # 测试简体字体
        print(f"\n🔤 简体字体提示词:")
        simplified_prompt = build_generation_prompt(content, style, position, high_resolution, "simplified")
        print(f"   {simplified_prompt}")

        # 测试繁体字体
        print(f"\n🔤 繁体字体提示词:")
        traditional_prompt = build_generation_prompt(content, style, position, high_resolution, "traditional")
        print(f"   {traditional_prompt}")

        # 验证差异
        print(f"\n📊 提示词对比:")
        print(f"   简体包含'简体': {'简体' in simplified_prompt}")
        print(f"   繁体包含'繁体': {'繁体' in traditional_prompt}")
        print(f"   简体不包含'繁体': {'繁体' not in simplified_prompt}")
        print(f"   繁体不包含'简体': {'简体' not in traditional_prompt}")

        # 验证其他内容相同
        simplified_base = simplified_prompt.replace("简体中文", "")
        traditional_base = traditional_prompt.replace("繁体中文", "")

        print(f"   除字体类型外内容相同: {simplified_base == traditional_base}")

        if (
            "简体" in simplified_prompt
            and "繁体" in traditional_prompt
            and "繁体" not in simplified_prompt
            and "简体" not in traditional_prompt
        ):
            print("✅ 字体类型正确添加到提示词中")
            return True
        else:
            print("❌ 字体类型未正确添加到提示词中")
            return False

    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback

        print("详细错误信息:")
        traceback.print_exc()
        return False


def test_api_schema():
    """测试API模式定义"""

    print("\n🔧 测试API模式定义...")
    print("=" * 60)

    try:
        from app.schemas.tattoo import TattooGenerationRequest

        # 测试创建请求对象
        request_data = {
            "request_id": 1,
            "style": "calligraphy",
            "position": "forearm",
            "content": "龙",
            "high_resolution": False,
            "font_type": "traditional",
        }

        request = TattooGenerationRequest(**request_data)

        print(f"✅ API模式创建成功:")
        print(f"   request_id: {request.request_id}")
        print(f"   content: {request.content}")
        print(f"   font_type: {request.font_type}")

        # 测试默认值
        default_request = TattooGenerationRequest(request_id=2, style="calligraphy", position="forearm", content="龙")

        print(f"\n✅ 默认字体类型: {default_request.font_type}")

        # 测试不同字体类型
        simplified_request = TattooGenerationRequest(
            request_id=3, style="calligraphy", position="forearm", content="龙", font_type="simplified"
        )

        print(f"✅ 简体字体类型: {simplified_request.font_type}")

        return True

    except Exception as e:
        print(f"❌ API模式测试失败: {e}")
        import traceback

        traceback.print_exc()
        return False


def test_different_scenarios():
    """测试不同场景的字体类型处理"""

    print("\n🔍 测试不同场景...")
    print("=" * 60)

    def build_prompt(content, font_type):
        font_type_text = "繁体" if font_type == "traditional" else "简体"
        return f"请为我生成文字'{content}'的前臂纹身图，{font_type_text}中文，中国传统书法风格，黑色墨水，艺术字体，适合纹身，文化传统，精美作品，最佳质量，超高细节，清晰锐利"

    test_cases = [("龙", "单字"), ("龙腾虎跃", "成语"), ("无为", "词语"), ("百折不挠", "四字成语")]

    for content, content_type in test_cases:
        print(f"\n📝 测试内容: {content} ({content_type})")

        # 简体提示词
        simplified = build_prompt(content, "simplified")

        # 繁体提示词
        traditional = build_prompt(content, "traditional")

        print(f"   简体: ...{simplified[-30:]}")
        print(f"   繁体: ...{traditional[-30:]}")

        # 验证字体类型标识
        has_simplified = "简体" in simplified
        has_traditional = "繁体" in traditional

        print(f"   ✅ 简体标识: {has_simplified}")
        print(f"   ✅ 繁体标识: {has_traditional}")

    return True


def main():
    """主函数"""

    print("🧪 字体类型功能测试（简化版）")
    print("=" * 60)

    # 测试1: 提示词生成逻辑
    test1_success = test_prompt_logic()

    # 测试2: API模式定义
    test2_success = test_api_schema()

    # 测试3: 不同场景
    test3_success = test_different_scenarios()

    print("\n" + "=" * 60)
    print("📋 测试总结:")
    print(f"   提示词生成逻辑测试: {'✅ 通过' if test1_success else '❌ 失败'}")
    print(f"   API模式定义测试: {'✅ 通过' if test2_success else '❌ 失败'}")
    print(f"   不同场景测试: {'✅ 通过' if test3_success else '❌ 失败'}")

    if test1_success and test2_success and test3_success:
        print("🎉 所有测试通过！字体类型功能正常")
        print("💡 功能说明:")
        print("   - 用户可以在身体部位选择对话框中选择简体或繁体")
        print("   - 选择繁体时，提示词会包含'繁体中文'要求")
        print("   - 选择简体时，提示词会包含'简体中文'要求")
        print("   - API支持font_type参数，默认为simplified")
    else:
        print("⚠️ 部分测试失败，需要检查实现")
    print("=" * 60)


if __name__ == "__main__":
    main()
