#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试Dashboard修复功能
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from sqlalchemy.orm import Session
from app.db.database import get_db
from app.models.tattoo import TattooImage, TattooRequest
from app.models.user import User


def test_tattoo_stats():
    """测试纹身统计API"""
    
    print("🧪 测试纹身统计API...")
    print("=" * 60)
    
    try:
        # 获取数据库会话
        db = next(get_db())
        
        # 查找一个用户
        user = db.query(User).first()
        if not user:
            print("❌ 没有找到用户")
            return False
            
        print(f"✅ 找到用户: {user.username}")
        
        # 模拟API逻辑：查询用户的纹身图片总数
        total_count = (
            db.query(TattooImage)
            .join(TattooRequest)
            .filter(TattooRequest.user_id == user.id)
            .count()
        )
        
        # 查询不同设计数（按content分组）
        unique_designs = (
            db.query(TattooImage.content)
            .join(TattooRequest)
            .filter(TattooRequest.user_id == user.id)
            .distinct()
            .count()
        )
        
        print(f"📊 统计结果:")
        print(f"   用户ID: {user.id}")
        print(f"   总图片数: {total_count}")
        print(f"   不同设计数: {unique_designs}")
        
        # 验证数据合理性
        if total_count >= unique_designs:
            print("✅ 数据逻辑正确：总图片数 >= 不同设计数")
        else:
            print("❌ 数据逻辑错误：总图片数 < 不同设计数")
            return False
        
        # 显示一些具体的图片信息
        if total_count > 0:
            print(f"\n📋 图片详情（前5张）:")
            images = (
                db.query(TattooImage)
                .join(TattooRequest)
                .filter(TattooRequest.user_id == user.id)
                .order_by(TattooImage.created_at.desc())
                .limit(5)
                .all()
            )
            
            for i, img in enumerate(images, 1):
                print(f"   {i}. 内容: {img.content}, 创建时间: {img.created_at}")
        
        return True
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        print("详细错误信息:")
        traceback.print_exc()
        return False
    finally:
        db.close()


def test_content_grouping():
    """测试内容分组逻辑"""
    
    print("\n🔍 测试内容分组逻辑...")
    print("=" * 60)
    
    try:
        db = next(get_db())
        
        # 查找一个用户
        user = db.query(User).first()
        if not user:
            print("❌ 没有找到用户")
            return False
        
        # 获取用户的所有图片，按内容分组
        images_by_content = {}
        images = (
            db.query(TattooImage)
            .join(TattooRequest)
            .filter(TattooRequest.user_id == user.id)
            .all()
        )
        
        for img in images:
            content = img.content
            if content not in images_by_content:
                images_by_content[content] = []
            images_by_content[content].append(img)
        
        print(f"📊 内容分组结果:")
        print(f"   总图片数: {len(images)}")
        print(f"   不同内容数: {len(images_by_content)}")
        
        if images_by_content:
            print(f"\n📋 分组详情:")
            for content, content_images in images_by_content.items():
                print(f"   '{content}': {len(content_images)}张图片")
                
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False
    finally:
        db.close()


def main():
    """主函数"""
    
    print("🧪 Dashboard修复功能测试")
    print("=" * 60)
    
    # 测试1: 纹身统计API
    test1_success = test_tattoo_stats()
    
    # 测试2: 内容分组逻辑
    test2_success = test_content_grouping()
    
    print("\n" + "=" * 60)
    print("📋 测试总结:")
    print(f"   纹身统计API测试: {'✅ 通过' if test1_success else '❌ 失败'}")
    print(f"   内容分组逻辑测试: {'✅ 通过' if test2_success else '❌ 失败'}")
    
    if test1_success and test2_success:
        print("🎉 所有测试通过！Dashboard统计功能正常")
        print("💡 现在前端应该能正确显示图片总数")
    else:
        print("⚠️ 部分测试失败，需要检查数据库或逻辑")
    print("=" * 60)


if __name__ == "__main__":
    main()
