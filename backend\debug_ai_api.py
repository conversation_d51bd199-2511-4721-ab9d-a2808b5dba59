#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试AI API连接问题
"""

import asyncio
import sys
import os
import httpx

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.core.config import settings


async def debug_ai_api():
    """调试AI API连接"""
    
    print("🔍 开始调试AI API连接...")
    print("=" * 60)
    
    # 检查配置
    print("📋 配置信息:")
    print(f"   API URL: {settings.TEXT_AI_API_URL}")
    print(f"   API Key: {settings.TEXT_AI_API_KEY[:20] if settings.TEXT_AI_API_KEY else 'None'}...")
    print(f"   Model: {settings.TEXT_AI_MODEL}")
    
    if not settings.TEXT_AI_API_KEY:
        print("❌ 错误：TEXT_AI_API_KEY 未配置")
        return False
    
    # 测试API连接
    print(f"\n🌐 测试API连接...")
    
    headers = {
        "Authorization": f"Bearer {settings.TEXT_AI_API_KEY}",
        "Content-Type": "application/json"
    }
    
    # 简单的测试请求
    test_data = {
        "model": settings.TEXT_AI_MODEL,
        "messages": [
            {"role": "user", "content": "你好，请回复'测试成功'"}
        ],
        "temperature": 0.7,
        "max_tokens": 100
    }
    
    try:
        async with httpx.AsyncClient(timeout=30.0) as client:
            print(f"   发送请求到: {settings.TEXT_AI_API_URL}/chat/completions")
            
            response = await client.post(
                f"{settings.TEXT_AI_API_URL}/chat/completions",
                headers=headers,
                json=test_data
            )
            
            print(f"   响应状态码: {response.status_code}")
            
            if response.status_code == 200:
                result = response.json()
                content = result.get("choices", [{}])[0].get("message", {}).get("content", "")
                print(f"   AI响应: {content}")
                print("✅ API连接测试成功！")
                return True
            else:
                print(f"   错误响应: {response.text}")
                print("❌ API连接测试失败")
                return False
                
    except Exception as e:
        print(f"   连接异常: {e}")
        print("❌ API连接测试失败")
        return False


async def test_recommendation_api():
    """测试推荐API"""
    
    print(f"\n🤖 测试推荐API...")
    
    from app.services.text_ai_service import TextAIService
    
    try:
        text_ai_service = TextAIService()
        
        # 测试推荐
        user_description = "我想要一个代表勇气的纹身"
        max_results = 2
        
        print(f"   用户描述: {user_description}")
        print(f"   推荐数量: {max_results}")
        
        result = await text_ai_service.recommend_characters(user_description, max_results)
        
        print("✅ 推荐API测试成功！")
        print(f"   汉字数量: {len(result.get('characters', []))}")
        print(f"   词语数量: {len(result.get('words', []))}")
        print(f"   成语数量: {len(result.get('idioms', []))}")
        
        return True
        
    except Exception as e:
        print(f"   推荐API异常: {e}")
        print("❌ 推荐API测试失败")
        return False


async def main():
    """主函数"""
    
    print("🧪 AI API 调试工具")
    print("=" * 60)
    
    # 测试基础连接
    connection_ok = await debug_ai_api()
    
    if connection_ok:
        # 测试推荐功能
        recommendation_ok = await test_recommendation_api()
        
        if recommendation_ok:
            print(f"\n🎉 所有测试通过！")
        else:
            print(f"\n❌ 推荐功能测试失败")
    else:
        print(f"\n❌ 基础连接测试失败")
    
    print("=" * 60)


if __name__ == "__main__":
    asyncio.run(main())
