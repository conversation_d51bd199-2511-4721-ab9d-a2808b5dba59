#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试配置加载
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.core.config import settings

def test_config():
    """测试配置"""
    
    print("🔍 检查配置加载...")
    print("=" * 50)
    
    print(f"TEXT_AI_API_URL: {settings.TEXT_AI_API_URL}")
    print(f"TEXT_AI_API_KEY: {settings.TEXT_AI_API_KEY[:20] if settings.TEXT_AI_API_KEY else 'None'}...")
    print(f"TEXT_AI_MODEL: {settings.TEXT_AI_MODEL}")
    
    print("=" * 50)
    
    if settings.TEXT_AI_API_KEY:
        print("✅ API密钥已配置")
        return True
    else:
        print("❌ API密钥未配置")
        return False

if __name__ == "__main__":
    test_config()
